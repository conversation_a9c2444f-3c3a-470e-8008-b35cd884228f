"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collections/[handle]/page",{

/***/ "(app-pages-browser)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Navigation_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Navigation.module.css */ \"(app-pages-browser)/./src/components/layout/Navigation.module.css\");\n/* harmony import */ var _Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../index */ \"(app-pages-browser)/./src/components/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Navigation(param) {\n    let { className } = param;\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileDropdowns, setMobileDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        company: false,\n        products: false,\n        discover: false\n    });\n    const [collections, setCollections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showMegaMenu, setShowMegaMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch collections for mega menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const fetchCollections = {\n                \"Navigation.useEffect.fetchCollections\": async ()=>{\n                    try {\n                        const response = await fetch('http://localhost:5000/api/collections?hierarchy=true');\n                        if (response.ok) {\n                            const data = await response.json();\n                            // Handle hierarchical response structure\n                            if (data.success && data.data) {\n                                if (data.data.hierarchy) {\n                                    // Hierarchical structure: data.data.collections\n                                    setCollections(data.data.collections || []);\n                                } else {\n                                    // Regular structure: data.data.collections or data.data\n                                    setCollections(data.data.collections || data.data || []);\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch collections:', error);\n                        setCollections([]); // Set empty array on error to prevent filter issues\n                    }\n                }\n            }[\"Navigation.useEffect.fetchCollections\"];\n            fetchCollections();\n        }\n    }[\"Navigation.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().navigation), \" \").concat(className || ''),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().navContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().logo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: \"Cast Stone\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Interiors & Decorations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().navMenuWrapper),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().navMenu),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownToggle),\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/contact\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/about\",\n                                                        children: \"Our Story\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/retail-locator\",\n                                                        children: \"Retail Locator\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/wholesale-signup\",\n                                                        children: \"Wholesale Sign-up\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdown), \" \").concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().megaDropdown)),\n                                    onMouseEnter: ()=>setShowMegaMenu(true),\n                                    onMouseLeave: ()=>setShowMegaMenu(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownToggle),\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        showMegaMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().megaMenu),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().megaMenuContent),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().megaMenuSection),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: \"Main Collections\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionsGrid),\n                                                                children: collections && collections.length > 0 ? collections.filter((c)=>c && c.level === 0).map((collection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionGroup),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/collections/\".concat(collection.handle),\n                                                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionTitle),\n                                                                                children: collection.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                lineNumber: 94,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            collection.children && collection.children.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().subCollectionsList),\n                                                                                children: collection.children.map((subCollection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                                href: \"/collections/\".concat(subCollection.handle),\n                                                                                                children: subCollection.title\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                lineNumber: 104,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            subCollection.children && subCollection.children.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().subSubCollectionsList),\n                                                                                                children: subCollection.children.map((subSubCollection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                                            href: \"/collections/\".concat(subSubCollection.handle),\n                                                                                                            children: subSubCollection.title\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                            lineNumber: 111,\n                                                                                                            columnNumber: 49\n                                                                                                        }, this)\n                                                                                                    }, subSubCollection._id, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                        lineNumber: 110,\n                                                                                                        columnNumber: 47\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                lineNumber: 108,\n                                                                                                columnNumber: 43\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, subCollection._id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                        lineNumber: 103,\n                                                                                        columnNumber: 39\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                lineNumber: 101,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, collection._id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 93,\n                                                                        columnNumber: 31\n                                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().loadingMessage),\n                                                                    children: \"Loading collections...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 88,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().megaMenuSection),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: \"Quick Links\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickLinks),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/products\",\n                                                                            children: \"All Products\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                            lineNumber: 132,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 132,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/collections\",\n                                                                            children: \"Browse Collections\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                            lineNumber: 133,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/catalog\",\n                                                                            children: \"Product Catalog\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                            lineNumber: 134,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 134,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/finishes\",\n                                                                            children: \"Available Finishes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                            lineNumber: 135,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 135,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/projects\",\n                                        children: \"Completed Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownToggle),\n                                            children: \"Discover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/catalog\",\n                                                        children: \"Catalog\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/finishes\",\n                                                        children: \"Finishes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/videos\",\n                                                        children: \"Videos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/technical\",\n                                                        children: \"Technical Info\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/faqs\",\n                                                        children: \"FAQs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().cartWrapper),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_4__.CartIcon, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().mobileMenuToggle), \" \").concat(mobileMenuOpen ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().mobileMenu), \" \").concat(mobileMenuOpen ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().mobileMenuLogo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: \"Cast Stone\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Interiors & Decorations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().mobileNavMenu),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().mobileDropdownToggle), \" \").concat(mobileDropdowns.company ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                        onClick: ()=>setMobileDropdowns((prev)=>({\n                                                    ...prev,\n                                                    company: !prev.company\n                                                })),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().mobileDropdownMenu), \" \").concat(mobileDropdowns.company ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/contact\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Contact Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/about\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Our Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/retail-locator\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Retail Locator\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/wholesale-signup\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Wholesale Sign-up\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().mobileDropdownToggle), \" \").concat(mobileDropdowns.products ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                        onClick: ()=>setMobileDropdowns((prev)=>({\n                                                    ...prev,\n                                                    products: !prev.products\n                                                })),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().mobileDropdownMenu), \" \").concat(mobileDropdowns.products ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/collections/architectural\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Architectural\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/collections/designer\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Designer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/collections/limited-edition\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Limited Edition\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/collections/cast-stone-sealers\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Cast Stone Sealers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"All Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/collections\",\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: \"Collections\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/projects\",\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: \"Completed Projects\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().mobileDropdownToggle), \" \").concat(mobileDropdowns.discover ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                        onClick: ()=>setMobileDropdowns((prev)=>({\n                                                    ...prev,\n                                                    discover: !prev.discover\n                                                })),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Discover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().mobileDropdownMenu), \" \").concat(mobileDropdowns.discover ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/catalog\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Catalog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/finishes\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Finishes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/videos\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Videos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/technical\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Technical Info\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/faqs\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"FAQs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_4__.CartSidebar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_s(Navigation, \"2jAupOyKPfdOp9Gy9ZC7+qSDY/o=\");\n_c = Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Navigation.tsx\n"));

/***/ })

});