'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Lock, Mail, Shield, AlertCircle, CheckCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import { adminAuth, useAdminAuth } from '../../../services/adminAuth';
import styles from './page.module.css';

const loginSchema = z.object({
  email: z.string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email is too long'),
  password: z.string()
    .min(1, 'Password is required')
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password is too long')
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function AdminLogin() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [attemptCount, setAttemptCount] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockTimeRemaining, setBlockTimeRemaining] = useState(0);

  // Use the professional auth service
  const { isAuthenticated, isLoading, error, login } = useAdminAuth();

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setError,
    clearErrors
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange'
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/admin/dashboard');
    }
  }, [isAuthenticated, router]);

  // Handle rate limiting
  useEffect(() => {
    if (isBlocked && blockTimeRemaining > 0) {
      const timer = setInterval(() => {
        setBlockTimeRemaining(prev => {
          if (prev <= 1) {
            setIsBlocked(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isBlocked, blockTimeRemaining]);

  const onSubmit = async (data: LoginFormData) => {
    // Check if blocked
    if (isBlocked) {
      toast.error(`Too many failed attempts. Please wait ${blockTimeRemaining} seconds.`);
      return;
    }

    // Clear any previous errors
    clearErrors();

    try {
      const result = await login(data);

      if (result.success && result.admin) {
        // Reset attempt count on successful login
        setAttemptCount(0);

        toast.success('Login successful!', {
          icon: '✅',
          duration: 2000,
        });

        // Check if password change is required
        if (result.admin.mustChangePassword) {
          toast.info('Password change required', {
            duration: 3000,
          });
          router.push('/admin/change-password');
        } else {
          router.push('/admin/dashboard');
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';

      // Increment attempt count
      const newAttemptCount = attemptCount + 1;
      setAttemptCount(newAttemptCount);

      // Handle specific error types
      if (errorMessage.toLowerCase().includes('invalid credentials')) {
        setError('email', { message: 'Invalid email or password' });
        setError('password', { message: 'Invalid email or password' });

        if (newAttemptCount >= 3) {
          setIsBlocked(true);
          setBlockTimeRemaining(30); // 30 seconds block
          toast.error('Too many failed attempts. Account temporarily blocked.', {
            duration: 5000,
          });
        } else {
          toast.error(`Invalid credentials. ${3 - newAttemptCount} attempts remaining.`, {
            duration: 4000,
          });
        }
      } else if (errorMessage.toLowerCase().includes('locked')) {
        toast.error('Account is locked. Please contact administrator.', {
          duration: 6000,
        });
      } else if (errorMessage.toLowerCase().includes('network')) {
        toast.error('Network error. Please check your connection and try again.', {
          duration: 5000,
        });
      } else {
        toast.error(errorMessage, {
          duration: 4000,
        });
      }
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.loginCard}>
        <div className={styles.header}>
          <div className={styles.logo}>
            <Shield className={styles.logoIcon} />
            <div>
              <h1 className={styles.logoTitle}>Cast Stone</h1>
              <p className={styles.logoSubtitle}>Admin Dashboard</p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
          <h2 className={styles.title}>Admin Login</h2>
          <p className={styles.subtitle}>
            Sign in to access the admin dashboard
          </p>

          {/* Global error display */}
          {error && (
            <div className={styles.errorAlert}>
              <AlertCircle className={styles.errorIcon} />
              <span>{error}</span>
            </div>
          )}

          {/* Rate limiting warning */}
          {isBlocked && (
            <div className={styles.warningAlert}>
              <AlertCircle className={styles.warningIcon} />
              <span>
                Too many failed attempts. Please wait {blockTimeRemaining} seconds before trying again.
              </span>
            </div>
          )}

          {/* Attempt counter */}
          {attemptCount > 0 && attemptCount < 3 && !isBlocked && (
            <div className={styles.infoAlert}>
              <AlertCircle className={styles.infoIcon} />
              <span>
                {3 - attemptCount} login attempts remaining
              </span>
            </div>
          )}

          <div className={styles.formGroup}>
            <label className={styles.label}>
              <Mail className={styles.labelIcon} />
              Email Address
            </label>
            <input
              {...register('email')}
              type="email"
              className={`${styles.input} ${errors.email ? styles.error : ''}`}
              placeholder="Enter your email"
              disabled={isLoading}
            />
            {errors.email && (
              <span className={styles.errorMessage}>{errors.email.message}</span>
            )}
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>
              <Lock className={styles.labelIcon} />
              Password
            </label>
            <div className={styles.passwordWrapper}>
              <input
                {...register('password')}
                type={showPassword ? 'text' : 'password'}
                className={`${styles.input} ${errors.password ? styles.error : ''}`}
                placeholder="Enter your password"
                disabled={isLoading}
              />
              <button
                type="button"
                className={styles.passwordToggle}
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? <EyeOff /> : <Eye />}
              </button>
            </div>
            {errors.password && (
              <span className={styles.errorMessage}>{errors.password.message}</span>
            )}
          </div>

          <button
            type="submit"
            className={`${styles.submitButton} ${isBlocked ? styles.blocked : ''}`}
            disabled={!isValid || isLoading || isBlocked}
          >
            {isLoading ? (
              <>
                <div className={styles.spinner}></div>
                <span>Signing in...</span>
              </>
            ) : isBlocked ? (
              <>
                <AlertCircle className={styles.buttonIcon} />
                <span>Blocked ({blockTimeRemaining}s)</span>
              </>
            ) : (
              <>
                <CheckCircle className={styles.buttonIcon} />
                <span>Sign In</span>
              </>
            )}
          </button>

          <div className={styles.footer}>
            <p className={styles.footerText}>
              Authorized personnel only. All activities are logged and monitored.
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}
