globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/collections/[handle]/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/../node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/../node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/cart/AddToCartButton.tsx":{"*":{"id":"(ssr)/./src/components/cart/AddToCartButton.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/cart/CartIcon.tsx":{"*":{"id":"(ssr)/./src/components/cart/CartIcon.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/cart/CartSidebar.tsx":{"*":{"id":"(ssr)/./src/components/cart/CartSidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/checkout/CheckoutForm.tsx":{"*":{"id":"(ssr)/./src/components/checkout/CheckoutForm.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/checkout/OrderSummary.tsx":{"*":{"id":"(ssr)/./src/components/checkout/OrderSummary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Footer.tsx":{"*":{"id":"(ssr)/./src/components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Navigation.tsx":{"*":{"id":"(ssr)/./src/components/layout/Navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/collections/[handle]/page.tsx":{"*":{"id":"(ssr)/./src/app/collections/[handle]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":"(app-pages-browser)/../node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\cart\\AddToCartButton.tsx":{"id":"(app-pages-browser)/./src/components/cart/AddToCartButton.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\cart\\CartIcon.tsx":{"id":"(app-pages-browser)/./src/components/cart/CartIcon.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\cart\\CartSidebar.tsx":{"id":"(app-pages-browser)/./src/components/cart/CartSidebar.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\checkout\\CheckoutForm.tsx":{"id":"(app-pages-browser)/./src/components/checkout/CheckoutForm.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\checkout\\OrderSummary.tsx":{"id":"(app-pages-browser)/./src/components/checkout/OrderSummary.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\CatalogSection.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\CollectionsGrid.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\FeaturedCollections.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\HeroSection.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\HomePage.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\TestimonialsSection.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\layout\\Footer.tsx":{"id":"(app-pages-browser)/./src/components/layout/Footer.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\layout\\Navigation.tsx":{"id":"(app-pages-browser)/./src/components/layout/Navigation.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\collections\\[handle]\\page.tsx":{"id":"(app-pages-browser)/./src/app/collections/[handle]/page.tsx","name":"*","chunks":["app/collections/[handle]/page","static/chunks/app/collections/%5Bhandle%5D/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\page":[{"inlined":false,"path":"static/css/app/page.css"}],"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\app\\collections\\[handle]\\page":[{"inlined":false,"path":"static/css/app/collections/[handle]/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(rsc)/../node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/cart/AddToCartButton.tsx":{"*":{"id":"(rsc)/./src/components/cart/AddToCartButton.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/cart/CartIcon.tsx":{"*":{"id":"(rsc)/./src/components/cart/CartIcon.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/cart/CartSidebar.tsx":{"*":{"id":"(rsc)/./src/components/cart/CartSidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/checkout/CheckoutForm.tsx":{"*":{"id":"(rsc)/./src/components/checkout/CheckoutForm.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/checkout/OrderSummary.tsx":{"*":{"id":"(rsc)/./src/components/checkout/OrderSummary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(rsc)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(rsc)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(rsc)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(rsc)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(rsc)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(rsc)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Footer.tsx":{"*":{"id":"(rsc)/./src/components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Navigation.tsx":{"*":{"id":"(rsc)/./src/components/layout/Navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/collections/[handle]/page.tsx":{"*":{"id":"(rsc)/./src/app/collections/[handle]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{"(app-pages-browser)/../node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/../node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}}}}