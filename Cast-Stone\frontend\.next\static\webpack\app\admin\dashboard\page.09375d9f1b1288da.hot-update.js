"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/admin/dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/dashboard/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _services_adminAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../services/adminAuth */ \"(app-pages-browser)/./src/services/adminAuth.ts\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/admin/dashboard/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDashboard() {\n    var _state_admin;\n    _s();\n    const { admin, hasPermission, logout, getToken } = (0,_services_adminAuth__WEBPACK_IMPORTED_MODULE_2__.useAdminAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            const fetchDashboardData = {\n                \"AdminDashboard.useEffect.fetchDashboardData\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        // Fetch real dashboard data from analytics API\n                        const token = getToken();\n                        const response = await fetch('http://localhost:5000/api/admin/analytics/dashboard', {\n                            headers: {\n                                'Authorization': \"Bearer \".concat(token),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            console.log('Dashboard API response:', data);\n                            if (data.success) {\n                                setStats({\n                                    totalProducts: data.data.summary.totalProducts || 0,\n                                    totalOrders: data.data.summary.totalOrders || 0,\n                                    totalUsers: data.data.summary.totalUsers || 0,\n                                    totalRevenue: data.data.summary.totalRevenue || 0,\n                                    recentOrders: data.data.recentOrders || [],\n                                    topProducts: data.data.topProducts || []\n                                });\n                            }\n                        } else {\n                            console.error('Failed to fetch dashboard data:', response.status);\n                            // Fallback to empty data\n                            setStats({\n                                totalProducts: 0,\n                                totalOrders: 0,\n                                totalUsers: 0,\n                                totalRevenue: 0,\n                                recentOrders: [],\n                                topProducts: []\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch dashboard data:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AdminDashboard.useEffect.fetchDashboardData\"];\n            fetchDashboardData();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().loading),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading dashboard...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this);\n    }\n    const statCards = [\n        {\n            title: 'Total Products',\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalProducts) || 0,\n            icon: _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'blue',\n            change: '+12%',\n            trend: 'up'\n        },\n        {\n            title: 'Total Orders',\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalOrders) || 0,\n            icon: _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'green',\n            change: '+8%',\n            trend: 'up'\n        },\n        {\n            title: 'Total Users',\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalUsers) || 0,\n            icon: _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'purple',\n            change: '+15%',\n            trend: 'up'\n        },\n        {\n            title: 'Total Revenue',\n            value: \"$\".concat(((stats === null || stats === void 0 ? void 0 : stats.totalRevenue) || 0).toLocaleString()),\n            icon: _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: 'orange',\n            change: '+22%',\n            trend: 'up'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dashboard),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().title),\n                                children: \"Dashboard Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().subtitle),\n                                children: [\n                                    \"Welcome back, \",\n                                    (_state_admin = state.admin) === null || _state_admin === void 0 ? void 0 : _state_admin.name,\n                                    \"! Here's what's happening with your store.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerActions),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().refreshButton),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                \"Last 30 days\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statsGrid),\n                children: statCards.map((card, index)=>{\n                    const Icon = card.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statCard), \" \").concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default())[card.color]),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statIcon),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statTrend),\n                                        children: [\n                                            card.trend === 'up' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 42\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 59\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: card.change\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statContent),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statValue),\n                                        children: card.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statTitle),\n                                        children: card.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().contentGrid),\n                children: [\n                    hasPermission('orders', 'read') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().card),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardTitle),\n                                        children: \"Recent Orders\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().viewAllButton),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View All\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardContent),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().table),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().tableHeader),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Order\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Customer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        stats === null || stats === void 0 ? void 0 : stats.recentOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().tableRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().orderNumber),\n                                                        children: order.orderNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: order.customer\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().orderTotal),\n                                                        children: [\n                                                            \"$\",\n                                                            order.total.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().status), \" \").concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default())[order.status]),\n                                                        children: order.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: new Date(order.date).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, order.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    hasPermission('products', 'read') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().card),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardTitle),\n                                        children: \"Top Products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().viewAllButton),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View All\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardContent),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productList),\n                                    children: stats === null || stats === void 0 ? void 0 : stats.topProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productItem),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productRank),\n                                                    children: [\n                                                        \"#\",\n                                                        index + 1\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productInfo),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productName),\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productStats),\n                                                            children: [\n                                                                product.sales,\n                                                                \" sales • $\",\n                                                                product.revenue.toLocaleString(),\n                                                                \" revenue\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, product.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"yHDXnIT1Z8aKcRjWBw0DkkMkrSo=\", false, function() {\n    return [\n        _services_adminAuth__WEBPACK_IMPORTED_MODULE_2__.useAdminAuth\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/dashboard/page.tsx\n"));

/***/ })

});