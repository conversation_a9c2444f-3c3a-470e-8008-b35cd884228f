"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collections/[handle]/page",{

/***/ "(app-pages-browser)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navigation_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navigation.module.css */ \"(app-pages-browser)/./src/components/layout/Navigation.module.css\");\n/* harmony import */ var _Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../index */ \"(app-pages-browser)/./src/components/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Navigation(param) {\n    let { className } = param;\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileDropdowns, setMobileDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        company: false,\n        products: false,\n        discover: false\n    });\n    const [collections, setCollections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showMegaMenu, setShowMegaMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch collections for mega menu\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const fetchCollections = {\n                \"Navigation.useEffect.fetchCollections\": async ()=>{\n                    try {\n                        const response = await fetch('http://localhost:5000/api/collections?hierarchy=true');\n                        if (response.ok) {\n                            const data = await response.json();\n                            // Handle hierarchical response structure\n                            if (data.success && data.data) {\n                                if (data.data.hierarchy) {\n                                    // Hierarchical structure: data.data.collections\n                                    setCollections(data.data.collections || []);\n                                } else {\n                                    // Regular structure: data.data.collections or data.data\n                                    setCollections(data.data.collections || data.data || []);\n                                }\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch collections:', error);\n                        setCollections([]); // Set empty array on error to prevent filter issues\n                    }\n                }\n            }[\"Navigation.useEffect.fetchCollections\"];\n            fetchCollections();\n        }\n    }[\"Navigation.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navigation), \" \").concat(className || ''),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().logo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: \"Cast Stone\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Interiors & Decorations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navMenuWrapper),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navMenu),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownToggle),\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/contact\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/about\",\n                                                        children: \"Our Story\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/retail-locator\",\n                                                        children: \"Retail Locator\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/wholesale-signup\",\n                                                        children: \"Wholesale Sign-up\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdown), \" \").concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().megaDropdown)),\n                                    onMouseEnter: ()=>setShowMegaMenu(true),\n                                    onMouseLeave: ()=>setShowMegaMenu(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownToggle),\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        showMegaMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().megaMenu),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().megaMenuContent),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().megaMenuSection),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: \"Main Collections\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionsGrid),\n                                                                children: collections && collections.length > 0 ? collections.filter((c)=>c && c.level === 0).map((collection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionGroup),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                href: \"/collections/\".concat(collection.handle),\n                                                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionTitle),\n                                                                                children: collection.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                lineNumber: 93,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            collection.children && collection.children.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().subCollectionsList),\n                                                                                children: collection.children.map((subCollection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                                href: \"/collections/\".concat(subCollection.handle),\n                                                                                                children: subCollection.title\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                lineNumber: 103,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            subCollection.children && subCollection.children.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().subSubCollectionsList),\n                                                                                                children: subCollection.children.map((subSubCollection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                                            href: \"/collections/\".concat(subSubCollection.handle),\n                                                                                                            children: subSubCollection.title\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                            lineNumber: 110,\n                                                                                                            columnNumber: 49\n                                                                                                        }, this)\n                                                                                                    }, subSubCollection._id, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                        lineNumber: 109,\n                                                                                                        columnNumber: 47\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                                lineNumber: 107,\n                                                                                                columnNumber: 43\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, subCollection._id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                        lineNumber: 102,\n                                                                                        columnNumber: 39\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                                lineNumber: 100,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, collection._id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 92,\n                                                                        columnNumber: 31\n                                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().loadingMessage),\n                                                                    children: \"Loading collections...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                    lineNumber: 124,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().megaMenuSection),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: \"Quick Links\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().quickLinks),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/products\",\n                                                                            children: \"All Products\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                            lineNumber: 131,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 131,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/collections\",\n                                                                            children: \"Browse Collections\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                            lineNumber: 132,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 132,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/catalog\",\n                                                                            children: \"Product Catalog\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                            lineNumber: 133,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"/finishes\",\n                                                                            children: \"Available Finishes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                            lineNumber: 134,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                        lineNumber: 134,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/projects\",\n                                        children: \"Completed Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownToggle),\n                                            children: \"Discover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownMenu),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/catalog\",\n                                                        children: \"Catalog\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/finishes\",\n                                                        children: \"Finishes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/videos\",\n                                                        children: \"Videos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/technical\",\n                                                        children: \"Technical Info\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/faqs\",\n                                                        children: \"FAQs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().cartWrapper),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_3__.CartIcon, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileMenuToggle), \" \").concat(mobileMenuOpen ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''),\n                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileMenu), \" \").concat(mobileMenuOpen ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileMenuLogo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: \"Cast Stone\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Interiors & Decorations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileNavMenu),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownToggle), \" \").concat(mobileDropdowns.company ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''),\n                                        onClick: ()=>setMobileDropdowns((prev)=>({\n                                                    ...prev,\n                                                    company: !prev.company\n                                                })),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownMenu), \" \").concat(mobileDropdowns.company ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/contact\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Contact Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/about\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Our Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/retail-locator\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Retail Locator\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/wholesale-signup\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Wholesale Sign-up\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownToggle), \" \").concat(mobileDropdowns.products ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''),\n                                        onClick: ()=>setMobileDropdowns((prev)=>({\n                                                    ...prev,\n                                                    products: !prev.products\n                                                })),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownMenu), \" \").concat(mobileDropdowns.products ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/collections/architectural\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Architectural\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/collections/designer\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Designer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/collections/limited-edition\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Limited Edition\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/collections/cast-stone-sealers\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Cast Stone Sealers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"All Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/collections\",\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: \"Collections\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/projects\",\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: \"Completed Projects\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownToggle), \" \").concat(mobileDropdowns.discover ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''),\n                                        onClick: ()=>setMobileDropdowns((prev)=>({\n                                                    ...prev,\n                                                    discover: !prev.discover\n                                                })),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Discover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"\".concat((_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownMenu), \" \").concat(mobileDropdowns.discover ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/catalog\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Catalog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/finishes\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Finishes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/videos\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Videos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/technical\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Technical Info\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/faqs\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"FAQs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_3__.CartSidebar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(Navigation, \"2jAupOyKPfdOp9Gy9ZC7+qSDY/o=\");\n_c = Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Navigation.tsx\n"));

/***/ })

});