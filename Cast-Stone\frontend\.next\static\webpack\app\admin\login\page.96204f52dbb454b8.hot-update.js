"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/login/page",{

/***/ "(app-pages-browser)/./src/services/adminAuth.ts":
/*!***********************************!*\
  !*** ./src/services/adminAuth.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminAuthService: () => (/* binding */ AdminAuthService),\n/* harmony export */   adminAuth: () => (/* binding */ adminAuth),\n/* harmony export */   useAdminAuth: () => (/* binding */ useAdminAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api */ \"(app-pages-browser)/./src/services/api.ts\");\n/**\n * Professional Admin Authentication Service\n * Handles all admin authentication operations with comprehensive error handling,\n * fallbacks, and professional API architecture\n */ \n\n// Configuration\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nconst TOKEN_KEY = 'adminToken';\nconst ADMIN_KEY = 'adminUser';\nconst MAX_RETRY_ATTEMPTS = 3;\nconst RETRY_DELAY = 1000; // 1 second\n// Utility functions\nconst sleep = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\nconst isTokenExpired = (token)=>{\n    try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return Date.now() >= payload.exp * 1000;\n    } catch (e) {\n        return true;\n    }\n};\nconst sanitizeError = (error)=>{\n    if (error instanceof _api__WEBPACK_IMPORTED_MODULE_1__.ApiError) {\n        return error.message;\n    }\n    if (error && typeof error === 'object' && 'message' in error) {\n        return error.message;\n    }\n    if (typeof error === 'string') {\n        return error;\n    }\n    return 'An unexpected error occurred';\n};\n// Cookie utilities\nconst cookies = {\n    set: function(name, value) {\n        let hours = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 8;\n        try {\n            if (typeof document === 'undefined') return;\n            const expires = new Date();\n            expires.setTime(expires.getTime() + hours * 60 * 60 * 1000);\n            document.cookie = \"\".concat(name, \"=\").concat(value, \"; expires=\").concat(expires.toUTCString(), \"; path=/; SameSite=Strict; Secure=\").concat(window.location.protocol === 'https:');\n        } catch (e) {\n        // Silent fail\n        }\n    },\n    get: (name)=>{\n        try {\n            if (typeof document === 'undefined') return null;\n            const nameEQ = name + '=';\n            const ca = document.cookie.split(';');\n            for(let i = 0; i < ca.length; i++){\n                let c = ca[i];\n                while(c.charAt(0) === ' ')c = c.substring(1, c.length);\n                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n            }\n            return null;\n        } catch (e) {\n            return null;\n        }\n    },\n    remove: (name)=>{\n        try {\n            if (typeof document === 'undefined') return;\n            document.cookie = \"\".concat(name, \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;\");\n        } catch (e) {\n        // Silent fail\n        }\n    }\n};\n// Storage utilities with fallbacks\nconst storage = {\n    get: (key)=>{\n        try {\n            if (false) {}\n            return localStorage.getItem(key) || sessionStorage.getItem(key);\n        } catch (e) {\n            return null;\n        }\n    },\n    set: (key, value)=>{\n        try {\n            if (false) {}\n            localStorage.setItem(key, value);\n            // Fallback to sessionStorage if localStorage fails\n            try {\n                localStorage.setItem(key, value);\n            } catch (e) {\n                sessionStorage.setItem(key, value);\n            }\n            // Also store in cookies for middleware access\n            if (key === TOKEN_KEY) {\n                cookies.set('adminToken', value);\n            }\n        } catch (e) {\n        // Silent fail if both storage methods fail\n        }\n    },\n    remove: (key)=>{\n        try {\n            if (false) {}\n            localStorage.removeItem(key);\n            sessionStorage.removeItem(key);\n            // Also remove from cookies\n            if (key === TOKEN_KEY) {\n                cookies.remove('adminToken');\n            }\n        } catch (e) {\n        // Silent fail\n        }\n    }\n};\n// API request utility with retry logic\nconst apiRequest = async function(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, retryCount = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    const defaultHeaders = {\n        'Content-Type': 'application/json',\n        ...options.headers || {}\n    };\n    // Add auth token if available\n    const token = storage.get(TOKEN_KEY);\n    if (token && !isTokenExpired(token)) {\n        defaultHeaders['Authorization'] = \"Bearer \".concat(token);\n    }\n    try {\n        const response = await fetch(url, {\n            ...options,\n            headers: defaultHeaders\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            throw new _api__WEBPACK_IMPORTED_MODULE_1__.ApiError(data.message || \"HTTP \".concat(response.status), response.status, data);\n        }\n        return data;\n    } catch (error) {\n        // Retry logic for network errors\n        if (retryCount < MAX_RETRY_ATTEMPTS && (error instanceof TypeError || // Network error\n        error instanceof _api__WEBPACK_IMPORTED_MODULE_1__.ApiError && error.status >= 500)) {\n            await sleep(RETRY_DELAY * Math.pow(2, retryCount)); // Exponential backoff\n            return apiRequest(endpoint, options, retryCount + 1);\n        }\n        throw error;\n    }\n};\n// Admin Authentication Service\nclass AdminAuthService {\n    static getInstance() {\n        if (!AdminAuthService.instance) {\n            AdminAuthService.instance = new AdminAuthService();\n        }\n        return AdminAuthService.instance;\n    }\n    // Subscribe to auth state changes\n    subscribe(listener) {\n        this.listeners.push(listener);\n        return ()=>{\n            this.listeners = this.listeners.filter((l)=>l !== listener);\n        };\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>{\n            try {\n                listener(this.authState);\n            } catch (error) {\n                console.error('Error in auth state listener:', error);\n            }\n        });\n    }\n    updateState(updates) {\n        const prevState = {\n            ...this.authState\n        };\n        this.authState = {\n            ...this.authState,\n            ...updates\n        };\n        console.log('Auth state updated:', {\n            from: {\n                isAuthenticated: prevState.isAuthenticated,\n                hasAdmin: !!prevState.admin\n            },\n            to: {\n                isAuthenticated: this.authState.isAuthenticated,\n                hasAdmin: !!this.authState.admin\n            }\n        });\n        // Notify listeners synchronously\n        this.notifyListeners();\n    }\n    // Initialize auth state from storage\n    async initialize() {\n        if (this.isInitialized) {\n            console.log('⚠️ Auth service already initialized, skipping...');\n            return;\n        }\n        console.log('🔄 Initializing auth service...');\n        this.isInitialized = true;\n        this.updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            const token = storage.get(TOKEN_KEY);\n            const adminData = storage.get(ADMIN_KEY);\n            console.log('📦 Storage check:', {\n                hasToken: !!token,\n                hasAdminData: !!adminData,\n                tokenExpired: token ? isTokenExpired(token) : 'no token'\n            });\n            if (token && adminData && !isTokenExpired(token)) {\n                const admin = JSON.parse(adminData);\n                console.log('✅ Valid stored auth found, setting authenticated state');\n                console.log('Admin:', admin.email, admin.role);\n                // Set authenticated state immediately with stored data\n                this.updateState({\n                    isAuthenticated: true,\n                    admin,\n                    token,\n                    isLoading: false,\n                    error: null\n                });\n                console.log('✅ Auth state set, verifying token in background...');\n                // Verify token with backend in background (don't block UI)\n                this.verifyToken().then((isValid)=>{\n                    console.log('🔍 Token verification result:', isValid);\n                    if (!isValid) {\n                        // Only clear auth if token verification explicitly fails\n                        // This prevents network issues from logging out users\n                        console.warn('❌ Token verification failed, clearing auth');\n                        this.clearAuth();\n                    } else {\n                        console.log('✅ Token verification successful');\n                    }\n                }).catch((error)=>{\n                    console.warn('⚠️ Token verification error (keeping auth):', error);\n                // Don't clear auth on network errors - keep user logged in\n                });\n            } else {\n                console.log('❌ No valid stored auth found, clearing state');\n                this.clearAuth();\n            }\n        } catch (error) {\n            console.error('❌ Auth initialization error:', error);\n            this.updateState({\n                isLoading: false,\n                error: sanitizeError(error)\n            });\n            this.clearAuth();\n        }\n    }\n    // Login with comprehensive error handling\n    async login(credentials) {\n        this.updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            var _credentials_email, _credentials_password;\n            // Validate credentials\n            if (!((_credentials_email = credentials.email) === null || _credentials_email === void 0 ? void 0 : _credentials_email.trim()) || !((_credentials_password = credentials.password) === null || _credentials_password === void 0 ? void 0 : _credentials_password.trim())) {\n                throw new Error('Email and password are required');\n            }\n            const response = await apiRequest('/admin/login', {\n                method: 'POST',\n                body: JSON.stringify(credentials)\n            });\n            if (response.success && response.token && response.admin) {\n                console.log('✅ Login successful, storing auth data');\n                console.log('Token:', response.token.substring(0, 30) + '...');\n                console.log('Admin:', response.admin.email, response.admin.role);\n                // Store auth data\n                storage.set(TOKEN_KEY, response.token);\n                storage.set(ADMIN_KEY, JSON.stringify(response.admin));\n                console.log('✅ Auth data stored, updating state');\n                this.updateState({\n                    isAuthenticated: true,\n                    admin: response.admin,\n                    token: response.token,\n                    isLoading: false,\n                    error: null\n                });\n                console.log('✅ Auth state updated:', {\n                    isAuthenticated: true,\n                    hasAdmin: !!response.admin,\n                    hasToken: !!response.token\n                });\n            } else {\n                throw new Error(response.message || 'Login failed');\n            }\n            return response;\n        } catch (error) {\n            const errorMessage = sanitizeError(error);\n            this.updateState({\n                isLoading: false,\n                error: errorMessage\n            });\n            throw new Error(errorMessage);\n        }\n    }\n    // Logout with cleanup\n    async logout() {\n        this.updateState({\n            isLoading: true\n        });\n        try {\n            // Attempt to notify backend\n            try {\n                await apiRequest('/admin/logout', {\n                    method: 'POST'\n                });\n            } catch (e) {\n            // Silent fail - logout locally even if backend fails\n            }\n        } finally{\n            this.clearAuth();\n        }\n    }\n    // Verify token validity\n    async verifyToken() {\n        try {\n            const token = storage.get(TOKEN_KEY);\n            if (!token || isTokenExpired(token)) {\n                return false;\n            }\n            const response = await apiRequest('/admin/verify-token');\n            if (response.success && response.admin) {\n                // Update admin data\n                storage.set(ADMIN_KEY, JSON.stringify(response.admin));\n                this.updateState({\n                    admin: response.admin\n                });\n                return true;\n            }\n            return false;\n        } catch (e) {\n            return false;\n        }\n    }\n    // Clear authentication data\n    clearAuth() {\n        storage.remove(TOKEN_KEY);\n        storage.remove(ADMIN_KEY);\n        this.updateState({\n            isAuthenticated: false,\n            admin: null,\n            token: null,\n            isLoading: false,\n            error: null\n        });\n    }\n    // Get current auth state\n    getState() {\n        return {\n            ...this.authState\n        };\n    }\n    // Check if user has specific permission\n    hasPermission(resource, action) {\n        var _this_authState_admin;\n        if (!((_this_authState_admin = this.authState.admin) === null || _this_authState_admin === void 0 ? void 0 : _this_authState_admin.permissions)) return false;\n        const resourcePerms = this.authState.admin.permissions[resource];\n        if (!resourcePerms) return false;\n        return resourcePerms[action] === true;\n    }\n    // Check if user has role\n    hasRole(role) {\n        var _this_authState_admin;\n        return ((_this_authState_admin = this.authState.admin) === null || _this_authState_admin === void 0 ? void 0 : _this_authState_admin.role) === role;\n    }\n    // Get auth token\n    getToken() {\n        return this.authState.token;\n    }\n    // Get admin user\n    getAdmin() {\n        return this.authState.admin;\n    }\n    constructor(){\n        this.authState = {\n            isAuthenticated: false,\n            admin: null,\n            token: null,\n            isLoading: false,\n            error: null\n        };\n        this.listeners = [];\n        this.isInitialized = false;\n    }\n}\n// Export singleton instance\nconst adminAuth = AdminAuthService.getInstance();\n// React hook for auth state\nconst useAdminAuth = ()=>{\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0___default().useState(adminAuth.getState());\n    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect({\n        \"useAdminAuth.useEffect\": ()=>{\n            const unsubscribe = adminAuth.subscribe(setState);\n            adminAuth.initialize(); // Initialize on first use\n            return unsubscribe;\n        }\n    }[\"useAdminAuth.useEffect\"], []);\n    return {\n        ...state,\n        login: adminAuth.login.bind(adminAuth),\n        logout: adminAuth.logout.bind(adminAuth),\n        hasPermission: adminAuth.hasPermission.bind(adminAuth),\n        hasRole: adminAuth.hasRole.bind(adminAuth)\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/adminAuth.ts\n"));

/***/ })

});