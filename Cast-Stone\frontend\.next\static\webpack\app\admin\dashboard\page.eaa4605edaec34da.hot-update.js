"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/admin/dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/dashboard/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Eye,Package,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _services_adminAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../services/adminAuth */ \"(app-pages-browser)/./src/services/adminAuth.ts\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/admin/dashboard/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDashboard() {\n    var _state_admin;\n    _s();\n    const { admin, hasPermission, logout, getToken } = (0,_services_adminAuth__WEBPACK_IMPORTED_MODULE_2__.useAdminAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            const fetchDashboardData = {\n                \"AdminDashboard.useEffect.fetchDashboardData\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        // Fetch real dashboard data from analytics API\n                        const response = await fetch('http://localhost:5000/api/admin/analytics/dashboard', {\n                            headers: {\n                                'Authorization': \"Bearer \".concat(localStorage.getItem('adminToken')),\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            console.log('Dashboard API response:', data);\n                            if (data.success) {\n                                setStats({\n                                    totalProducts: data.data.summary.totalProducts || 0,\n                                    totalOrders: data.data.summary.totalOrders || 0,\n                                    totalUsers: data.data.summary.totalUsers || 0,\n                                    totalRevenue: data.data.summary.totalRevenue || 0,\n                                    recentOrders: data.data.recentOrders || [],\n                                    topProducts: data.data.topProducts || []\n                                });\n                            }\n                        } else {\n                            console.error('Failed to fetch dashboard data:', response.status);\n                            // Fallback to empty data\n                            setStats({\n                                totalProducts: 0,\n                                totalOrders: 0,\n                                totalUsers: 0,\n                                totalRevenue: 0,\n                                recentOrders: [],\n                                topProducts: []\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch dashboard data:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AdminDashboard.useEffect.fetchDashboardData\"];\n            fetchDashboardData();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().loading),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading dashboard...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    const statCards = [\n        {\n            title: 'Total Products',\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalProducts) || 0,\n            icon: _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'blue',\n            change: '+12%',\n            trend: 'up'\n        },\n        {\n            title: 'Total Orders',\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalOrders) || 0,\n            icon: _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'green',\n            change: '+8%',\n            trend: 'up'\n        },\n        {\n            title: 'Total Users',\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalUsers) || 0,\n            icon: _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'purple',\n            change: '+15%',\n            trend: 'up'\n        },\n        {\n            title: 'Total Revenue',\n            value: \"$\".concat(((stats === null || stats === void 0 ? void 0 : stats.totalRevenue) || 0).toLocaleString()),\n            icon: _barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: 'orange',\n            change: '+22%',\n            trend: 'up'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().dashboard),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().title),\n                                children: \"Dashboard Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().subtitle),\n                                children: [\n                                    \"Welcome back, \",\n                                    (_state_admin = state.admin) === null || _state_admin === void 0 ? void 0 : _state_admin.name,\n                                    \"! Here's what's happening with your store.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerActions),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().refreshButton),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                \"Last 30 days\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statsGrid),\n                children: statCards.map((card, index)=>{\n                    const Icon = card.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statCard), \" \").concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default())[card.color]),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statIcon),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statTrend),\n                                        children: [\n                                            card.trend === 'up' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 42\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 59\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: card.change\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statContent),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statValue),\n                                        children: card.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statTitle),\n                                        children: card.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().contentGrid),\n                children: [\n                    hasPermission('orders', 'read') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().card),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardTitle),\n                                        children: \"Recent Orders\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().viewAllButton),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View All\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardContent),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().table),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().tableHeader),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Order\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Customer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this),\n                                        stats === null || stats === void 0 ? void 0 : stats.recentOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().tableRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().orderNumber),\n                                                        children: order.orderNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: order.customer\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().orderTotal),\n                                                        children: [\n                                                            \"$\",\n                                                            order.total.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().status), \" \").concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default())[order.status]),\n                                                        children: order.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: new Date(order.date).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, order.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this),\n                    hasPermission('products', 'read') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().card),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardTitle),\n                                        children: \"Top Products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().viewAllButton),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Eye_Package_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View All\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().cardContent),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productList),\n                                    children: stats === null || stats === void 0 ? void 0 : stats.topProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productItem),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productRank),\n                                                    children: [\n                                                        \"#\",\n                                                        index + 1\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productInfo),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productName),\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productStats),\n                                                            children: [\n                                                                product.sales,\n                                                                \" sales • $\",\n                                                                product.revenue.toLocaleString(),\n                                                                \" revenue\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, product.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"yHDXnIT1Z8aKcRjWBw0DkkMkrSo=\", false, function() {\n    return [\n        _services_adminAuth__WEBPACK_IMPORTED_MODULE_2__.useAdminAuth\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/adminAuth.ts":
/*!***********************************!*\
  !*** ./src/services/adminAuth.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminAuthService: () => (/* binding */ AdminAuthService),\n/* harmony export */   adminAuth: () => (/* binding */ adminAuth),\n/* harmony export */   useAdminAuth: () => (/* binding */ useAdminAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api */ \"(app-pages-browser)/./src/services/api.ts\");\n/**\n * Professional Admin Authentication Service\n * Handles all admin authentication operations with comprehensive error handling,\n * fallbacks, and professional API architecture\n */ \n\n// Configuration\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nconst TOKEN_KEY = 'adminToken';\nconst ADMIN_KEY = 'adminUser';\nconst MAX_RETRY_ATTEMPTS = 3;\nconst RETRY_DELAY = 1000; // 1 second\n// Utility functions\nconst sleep = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\nconst isTokenExpired = (token)=>{\n    try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return Date.now() >= payload.exp * 1000;\n    } catch (e) {\n        return true;\n    }\n};\nconst sanitizeError = (error)=>{\n    if (error instanceof _api__WEBPACK_IMPORTED_MODULE_1__.ApiError) {\n        return error.message;\n    }\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        return error.message;\n    }\n    if (typeof error === 'string') {\n        return error;\n    }\n    return 'An unexpected error occurred';\n};\n// Storage utilities with fallbacks\nconst storage = {\n    get: (key)=>{\n        try {\n            if (false) {}\n            return localStorage.getItem(key) || sessionStorage.getItem(key);\n        } catch (e) {\n            return null;\n        }\n    },\n    set: (key, value)=>{\n        try {\n            if (false) {}\n            localStorage.setItem(key, value);\n            // Fallback to sessionStorage if localStorage fails\n            try {\n                localStorage.setItem(key, value);\n            } catch (e) {\n                sessionStorage.setItem(key, value);\n            }\n        } catch (e) {\n        // Silent fail if both storage methods fail\n        }\n    },\n    remove: (key)=>{\n        try {\n            if (false) {}\n            localStorage.removeItem(key);\n            sessionStorage.removeItem(key);\n        } catch (e) {\n        // Silent fail\n        }\n    }\n};\n// API request utility with retry logic\nconst apiRequest = async function(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, retryCount = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    const defaultHeaders = {\n        'Content-Type': 'application/json',\n        ...options.headers\n    };\n    // Add auth token if available\n    const token = storage.get(TOKEN_KEY);\n    if (token && !isTokenExpired(token)) {\n        defaultHeaders['Authorization'] = \"Bearer \".concat(token);\n    }\n    try {\n        const response = await fetch(url, {\n            ...options,\n            headers: defaultHeaders\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            throw new _api__WEBPACK_IMPORTED_MODULE_1__.ApiError(data.message || \"HTTP \".concat(response.status), response.status, data);\n        }\n        return data;\n    } catch (error) {\n        // Retry logic for network errors\n        if (retryCount < MAX_RETRY_ATTEMPTS && (error instanceof TypeError || // Network error\n        error instanceof _api__WEBPACK_IMPORTED_MODULE_1__.ApiError && error.status >= 500)) {\n            await sleep(RETRY_DELAY * Math.pow(2, retryCount)); // Exponential backoff\n            return apiRequest(endpoint, options, retryCount + 1);\n        }\n        throw error;\n    }\n};\n// Admin Authentication Service\nclass AdminAuthService {\n    static getInstance() {\n        if (!AdminAuthService.instance) {\n            AdminAuthService.instance = new AdminAuthService();\n        }\n        return AdminAuthService.instance;\n    }\n    // Subscribe to auth state changes\n    subscribe(listener) {\n        this.listeners.push(listener);\n        return ()=>{\n            this.listeners = this.listeners.filter((l)=>l !== listener);\n        };\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(this.authState));\n    }\n    updateState(updates) {\n        this.authState = {\n            ...this.authState,\n            ...updates\n        };\n        this.notifyListeners();\n    }\n    // Initialize auth state from storage\n    async initialize() {\n        this.updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            const token = storage.get(TOKEN_KEY);\n            const adminData = storage.get(ADMIN_KEY);\n            if (token && adminData && !isTokenExpired(token)) {\n                const admin = JSON.parse(adminData);\n                // Verify token with backend\n                try {\n                    await this.verifyToken();\n                    this.updateState({\n                        isAuthenticated: true,\n                        admin,\n                        token,\n                        isLoading: false,\n                        error: null\n                    });\n                } catch (e) {\n                    // Token invalid, clear storage\n                    this.clearAuth();\n                }\n            } else {\n                this.clearAuth();\n            }\n        } catch (error) {\n            this.updateState({\n                isLoading: false,\n                error: sanitizeError(error)\n            });\n            this.clearAuth();\n        }\n    }\n    // Login with comprehensive error handling\n    async login(credentials) {\n        this.updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            var _credentials_email, _credentials_password;\n            // Validate credentials\n            if (!((_credentials_email = credentials.email) === null || _credentials_email === void 0 ? void 0 : _credentials_email.trim()) || !((_credentials_password = credentials.password) === null || _credentials_password === void 0 ? void 0 : _credentials_password.trim())) {\n                throw new Error('Email and password are required');\n            }\n            const response = await apiRequest('/admin/login', {\n                method: 'POST',\n                body: JSON.stringify(credentials)\n            });\n            if (response.success && response.token && response.admin) {\n                // Store auth data\n                storage.set(TOKEN_KEY, response.token);\n                storage.set(ADMIN_KEY, JSON.stringify(response.admin));\n                this.updateState({\n                    isAuthenticated: true,\n                    admin: response.admin,\n                    token: response.token,\n                    isLoading: false,\n                    error: null\n                });\n            } else {\n                throw new Error(response.message || 'Login failed');\n            }\n            return response;\n        } catch (error) {\n            const errorMessage = sanitizeError(error);\n            this.updateState({\n                isLoading: false,\n                error: errorMessage\n            });\n            throw new Error(errorMessage);\n        }\n    }\n    // Logout with cleanup\n    async logout() {\n        this.updateState({\n            isLoading: true\n        });\n        try {\n            // Attempt to notify backend\n            try {\n                await apiRequest('/admin/logout', {\n                    method: 'POST'\n                });\n            } catch (e) {\n            // Silent fail - logout locally even if backend fails\n            }\n        } finally{\n            this.clearAuth();\n        }\n    }\n    // Verify token validity\n    async verifyToken() {\n        try {\n            const token = storage.get(TOKEN_KEY);\n            if (!token || isTokenExpired(token)) {\n                return false;\n            }\n            const response = await apiRequest('/admin/verify-token');\n            if (response.success && response.admin) {\n                // Update admin data\n                storage.set(ADMIN_KEY, JSON.stringify(response.admin));\n                this.updateState({\n                    admin: response.admin\n                });\n                return true;\n            }\n            return false;\n        } catch (e) {\n            return false;\n        }\n    }\n    // Clear authentication data\n    clearAuth() {\n        storage.remove(TOKEN_KEY);\n        storage.remove(ADMIN_KEY);\n        this.updateState({\n            isAuthenticated: false,\n            admin: null,\n            token: null,\n            isLoading: false,\n            error: null\n        });\n    }\n    // Get current auth state\n    getState() {\n        return {\n            ...this.authState\n        };\n    }\n    // Check if user has specific permission\n    hasPermission(resource, action) {\n        var _this_authState_admin;\n        if (!((_this_authState_admin = this.authState.admin) === null || _this_authState_admin === void 0 ? void 0 : _this_authState_admin.permissions)) return false;\n        const resourcePerms = this.authState.admin.permissions[resource];\n        if (!resourcePerms) return false;\n        return resourcePerms[action] === true;\n    }\n    // Check if user has role\n    hasRole(role) {\n        var _this_authState_admin;\n        return ((_this_authState_admin = this.authState.admin) === null || _this_authState_admin === void 0 ? void 0 : _this_authState_admin.role) === role;\n    }\n    // Get auth token\n    getToken() {\n        return this.authState.token;\n    }\n    // Get admin user\n    getAdmin() {\n        return this.authState.admin;\n    }\n    constructor(){\n        this.authState = {\n            isAuthenticated: false,\n            admin: null,\n            token: null,\n            isLoading: false,\n            error: null\n        };\n        this.listeners = [];\n    }\n}\n// Export singleton instance\nconst adminAuth = AdminAuthService.getInstance();\n// React hook for auth state\nconst useAdminAuth = ()=>{\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0___default().useState(adminAuth.getState());\n    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect({\n        \"useAdminAuth.useEffect\": ()=>{\n            const unsubscribe = adminAuth.subscribe(setState);\n            adminAuth.initialize(); // Initialize on first use\n            return unsubscribe;\n        }\n    }[\"useAdminAuth.useEffect\"], []);\n    return {\n        ...state,\n        login: adminAuth.login.bind(adminAuth),\n        logout: adminAuth.logout.bind(adminAuth),\n        hasPermission: adminAuth.hasPermission.bind(adminAuth),\n        hasRole: adminAuth.hasRole.bind(adminAuth)\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/adminAuth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   addToOfflineQueue: () => (/* binding */ addToOfflineQueue),\n/* harmony export */   cartApi: () => (/* binding */ cartApi),\n/* harmony export */   collectionsApi: () => (/* binding */ collectionsApi),\n/* harmony export */   offlineQueue: () => (/* binding */ offlineQueue),\n/* harmony export */   ordersApi: () => (/* binding */ ordersApi),\n/* harmony export */   processOfflineQueue: () => (/* binding */ processOfflineQueue),\n/* harmony export */   productsApi: () => (/* binding */ productsApi),\n/* harmony export */   retryRequest: () => (/* binding */ retryRequest)\n/* harmony export */ });\n/* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-explicit-any */ const API_BASE_URL = 'http://localhost:5000/api';\n// Error handling utility\nclass ApiError extends Error {\n    constructor(message, status, code){\n        super(message), this.status = status, this.code = code;\n        this.name = 'ApiError';\n    }\n}\n// Request utility with error handling\nasync function apiRequest(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    // Get auth token from localStorage\n    const token =  true ? localStorage.getItem('authToken') : 0;\n    const defaultHeaders = {\n        'Content-Type': 'application/json'\n    };\n    if (token) {\n        defaultHeaders.Authorization = \"Bearer \".concat(token);\n    }\n    const config = {\n        ...options,\n        headers: {\n            ...defaultHeaders,\n            ...options.headers\n        }\n    };\n    try {\n        const response = await fetch(url, config);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new ApiError(errorData.message || \"HTTP \".concat(response.status, \": \").concat(response.statusText), response.status, errorData.code);\n        }\n        const data = await response.json();\n        if (!data.success) {\n            throw new ApiError(data.message || 'API request failed', undefined, data.code);\n        }\n        return data;\n    } catch (error) {\n        if (error instanceof ApiError) {\n            throw error;\n        }\n        // Network or other errors\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n            throw new ApiError('Network error. Please check your connection.', 0, 'NETWORK_ERROR');\n        }\n        throw new ApiError('An unexpected error occurred.', 0, 'UNKNOWN_ERROR');\n    }\n}\n// Cart API\nconst cartApi = {\n    // Get user's cart\n    async getCart () {\n        return apiRequest('/cart');\n    },\n    // Add item to cart\n    async addToCart (productId) {\n        let quantity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        return apiRequest('/cart/add', {\n            method: 'POST',\n            body: JSON.stringify({\n                productId,\n                quantity\n            })\n        });\n    },\n    // Update item quantity\n    async updateCartItem (productId, quantity) {\n        return apiRequest(\"/cart/item/\".concat(productId), {\n            method: 'PUT',\n            body: JSON.stringify({\n                quantity\n            })\n        });\n    },\n    // Remove item from cart\n    async removeFromCart (productId) {\n        return apiRequest(\"/cart/item/\".concat(productId), {\n            method: 'DELETE'\n        });\n    },\n    // Clear entire cart\n    async clearCart () {\n        return apiRequest('/cart/clear', {\n            method: 'DELETE'\n        });\n    },\n    // Sync cart with frontend\n    async syncCart (items) {\n        return apiRequest('/cart/sync', {\n            method: 'POST',\n            body: JSON.stringify({\n                items\n            })\n        });\n    }\n};\n// Orders API\nconst ordersApi = {\n    // Create payment intent\n    async createPaymentIntent (amount) {\n        const response = await apiRequest('/orders/payment-intent', {\n            method: 'POST',\n            body: JSON.stringify({\n                amount\n            })\n        });\n        return response;\n    },\n    // Create order after successful payment\n    async createOrder (paymentIntentId, shippingAddress, paymentMethod) {\n        return apiRequest('/orders/create', {\n            method: 'POST',\n            body: JSON.stringify({\n                paymentIntentId,\n                shippingAddress,\n                paymentMethod\n            })\n        });\n    },\n    // Get user's orders\n    async getUserOrders () {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        return apiRequest(\"/orders?page=\".concat(page, \"&limit=\").concat(limit));\n    },\n    // Get specific order\n    async getOrder (orderNumber) {\n        return apiRequest(\"/orders/\".concat(orderNumber));\n    },\n    // Handle payment failure\n    async handlePaymentFailure (paymentIntentId, error) {\n        return apiRequest('/orders/payment-failure', {\n            method: 'POST',\n            body: JSON.stringify({\n                paymentIntentId,\n                error\n            })\n        });\n    }\n};\n// Products API (if needed for cart integration)\nconst productsApi = {\n    // Get all products\n    async getProducts (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.tags) searchParams.append('tags', params.tags);\n        if (params === null || params === void 0 ? void 0 : params.collectionId) searchParams.append('collectionId', params.collectionId);\n        if (params === null || params === void 0 ? void 0 : params.collectionPath) searchParams.append('collectionPath', params.collectionPath);\n        if (params === null || params === void 0 ? void 0 : params.includeDescendants) searchParams.append('includeDescendants', params.includeDescendants.toString());\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.sortBy) searchParams.append('sortBy', params.sortBy);\n        if (params === null || params === void 0 ? void 0 : params.sortOrder) searchParams.append('sortOrder', params.sortOrder);\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/products?\".concat(queryString) : '/products';\n        return apiRequest(endpoint);\n    },\n    // Get products by collection path\n    async getProductsByCollectionPath (path, params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.sortBy) searchParams.append('sortBy', params.sortBy);\n        if (params === null || params === void 0 ? void 0 : params.sortOrder) searchParams.append('sortOrder', params.sortOrder);\n        if (params === null || params === void 0 ? void 0 : params.includeDescendants) searchParams.append('includeDescendants', params.includeDescendants.toString());\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/products/collection/\".concat(path, \"?\").concat(queryString) : \"/products/collection/\".concat(path);\n        return apiRequest(endpoint);\n    },\n    // Get single product\n    async getProduct (id) {\n        return apiRequest(\"/products/\".concat(id));\n    }\n};\n// Collections API for hierarchical collections\nconst collectionsApi = {\n    // Get all collections with hierarchy support\n    async getCollections (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if ((params === null || params === void 0 ? void 0 : params.level) !== undefined) searchParams.append('level', params.level.toString());\n        if (params === null || params === void 0 ? void 0 : params.parentId) searchParams.append('parentId', params.parentId);\n        if (params === null || params === void 0 ? void 0 : params.hierarchy) searchParams.append('hierarchy', params.hierarchy.toString());\n        if ((params === null || params === void 0 ? void 0 : params.published) !== undefined) searchParams.append('published', params.published.toString());\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/collections?\".concat(queryString) : '/collections';\n        return apiRequest(endpoint);\n    },\n    // Get collection hierarchy\n    async getCollectionHierarchy () {\n        return apiRequest('/collections?hierarchy=true');\n    },\n    // Get collections by level\n    async getCollectionsByLevel (level) {\n        return apiRequest(\"/collections?level=\".concat(level));\n    },\n    // Get root collections\n    async getRootCollections () {\n        return apiRequest('/collections?level=0&published=true');\n    },\n    // Get single collection\n    async getCollection (id) {\n        const response = await apiRequest(\"/collections/\".concat(id));\n        // return { collection: response.data };\n        return {\n            collection: response.data\n        };\n    },\n    // Get collection by path\n    async getCollectionByPath (path) {\n        return apiRequest(\"/collections/path/\".concat(path));\n    },\n    // Get collection breadcrumbs\n    async getCollectionBreadcrumbs (id) {\n        const response = await apiRequest(\"/collections/\".concat(id, \"/breadcrumbs\"));\n        // return { breadcrumbs: response.data };\n        return {\n            breadcrumbs: response.data\n        };\n    }\n};\n// Retry utility for failed requests\nconst retryRequest = async function(requestFn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await requestFn();\n        } catch (error) {\n            lastError = error;\n            // Don't retry on client errors (4xx) except 408, 429\n            if (error instanceof ApiError && error.status) {\n                if (error.status >= 400 && error.status < 500 && error.status !== 408 && error.status !== 429) {\n                    throw error;\n                }\n            }\n            if (i < maxRetries) {\n                await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n            }\n        }\n    }\n    throw lastError;\n};\n// Offline detection and queue\nconst offlineQueue = [];\nconst addToOfflineQueue = (requestFn)=>{\n    offlineQueue.push(requestFn);\n};\nconst processOfflineQueue = async ()=>{\n    if (!navigator.onLine || offlineQueue.length === 0) return;\n    const requests = [\n        ...offlineQueue\n    ];\n    offlineQueue.length = 0; // Clear the queue\n    for (const request of requests){\n        try {\n            await request();\n        } catch (error) {\n            console.error('Failed to process offline request:', error);\n            // Re-add to queue if it fails\n            offlineQueue.push(request);\n        }\n    }\n};\n// Listen for online events to process queue\nif (true) {\n    window.addEventListener('online', processOfflineQueue);\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ })

});