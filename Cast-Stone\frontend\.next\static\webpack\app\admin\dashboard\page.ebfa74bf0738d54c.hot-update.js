"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/services/adminAuth.ts":
/*!***********************************!*\
  !*** ./src/services/adminAuth.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminAuthService: () => (/* binding */ AdminAuthService),\n/* harmony export */   adminAuth: () => (/* binding */ adminAuth),\n/* harmony export */   useAdminAuth: () => (/* binding */ useAdminAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api */ \"(app-pages-browser)/./src/services/api.ts\");\n/**\n * Professional Admin Authentication Service\n * Handles all admin authentication operations with comprehensive error handling,\n * fallbacks, and professional API architecture\n */ \n\n// Configuration\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nconst TOKEN_KEY = 'adminToken';\nconst ADMIN_KEY = 'adminUser';\nconst MAX_RETRY_ATTEMPTS = 3;\nconst RETRY_DELAY = 1000; // 1 second\n// Utility functions\nconst sleep = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\nconst isTokenExpired = (token)=>{\n    try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return Date.now() >= payload.exp * 1000;\n    } catch (e) {\n        return true;\n    }\n};\nconst sanitizeError = (error)=>{\n    if (error instanceof _api__WEBPACK_IMPORTED_MODULE_1__.ApiError) {\n        return error.message;\n    }\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        return error.message;\n    }\n    if (typeof error === 'string') {\n        return error;\n    }\n    return 'An unexpected error occurred';\n};\n// Storage utilities with fallbacks\nconst storage = {\n    get: (key)=>{\n        try {\n            if (false) {}\n            return localStorage.getItem(key) || sessionStorage.getItem(key);\n        } catch (e) {\n            return null;\n        }\n    },\n    set: (key, value)=>{\n        try {\n            if (false) {}\n            localStorage.setItem(key, value);\n            // Fallback to sessionStorage if localStorage fails\n            try {\n                localStorage.setItem(key, value);\n            } catch (e) {\n                sessionStorage.setItem(key, value);\n            }\n        } catch (e) {\n        // Silent fail if both storage methods fail\n        }\n    },\n    remove: (key)=>{\n        try {\n            if (false) {}\n            localStorage.removeItem(key);\n            sessionStorage.removeItem(key);\n        } catch (e) {\n        // Silent fail\n        }\n    }\n};\n// API request utility with retry logic\nconst apiRequest = async function(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, retryCount = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    const defaultHeaders = {\n        'Content-Type': 'application/json',\n        ...options.headers\n    };\n    // Add auth token if available\n    const token = storage.get(TOKEN_KEY);\n    if (token && !isTokenExpired(token)) {\n        defaultHeaders['Authorization'] = \"Bearer \".concat(token);\n    }\n    try {\n        const response = await fetch(url, {\n            ...options,\n            headers: defaultHeaders\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            throw new _api__WEBPACK_IMPORTED_MODULE_1__.ApiError(data.message || \"HTTP \".concat(response.status), response.status, data);\n        }\n        return data;\n    } catch (error) {\n        // Retry logic for network errors\n        if (retryCount < MAX_RETRY_ATTEMPTS && (error instanceof TypeError || // Network error\n        error instanceof _api__WEBPACK_IMPORTED_MODULE_1__.ApiError && error.status >= 500)) {\n            await sleep(RETRY_DELAY * Math.pow(2, retryCount)); // Exponential backoff\n            return apiRequest(endpoint, options, retryCount + 1);\n        }\n        throw error;\n    }\n};\n// Admin Authentication Service\nclass AdminAuthService {\n    static getInstance() {\n        if (!AdminAuthService.instance) {\n            AdminAuthService.instance = new AdminAuthService();\n        }\n        return AdminAuthService.instance;\n    }\n    // Subscribe to auth state changes\n    subscribe(listener) {\n        this.listeners.push(listener);\n        return ()=>{\n            this.listeners = this.listeners.filter((l)=>l !== listener);\n        };\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(this.authState));\n    }\n    updateState(updates) {\n        this.authState = {\n            ...this.authState,\n            ...updates\n        };\n        this.notifyListeners();\n    }\n    // Initialize auth state from storage\n    async initialize() {\n        console.log('🔄 Initializing auth service...');\n        this.updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            const token = storage.get(TOKEN_KEY);\n            const adminData = storage.get(ADMIN_KEY);\n            console.log('📦 Storage check:', {\n                hasToken: !!token,\n                hasAdminData: !!adminData,\n                tokenExpired: token ? isTokenExpired(token) : 'no token'\n            });\n            if (token && adminData && !isTokenExpired(token)) {\n                const admin = JSON.parse(adminData);\n                console.log('✅ Valid stored auth found, setting authenticated state');\n                console.log('Admin:', admin.email, admin.role);\n                // Set authenticated state immediately with stored data\n                this.updateState({\n                    isAuthenticated: true,\n                    admin,\n                    token,\n                    isLoading: false,\n                    error: null\n                });\n                console.log('✅ Auth state set, verifying token in background...');\n                // Verify token with backend in background (don't block UI)\n                this.verifyToken().then((isValid)=>{\n                    console.log('🔍 Token verification result:', isValid);\n                    if (!isValid) {\n                        // Only clear auth if token verification explicitly fails\n                        // This prevents network issues from logging out users\n                        console.warn('❌ Token verification failed, clearing auth');\n                        this.clearAuth();\n                    } else {\n                        console.log('✅ Token verification successful');\n                    }\n                }).catch((error)=>{\n                    console.warn('⚠️ Token verification error (keeping auth):', error);\n                // Don't clear auth on network errors - keep user logged in\n                });\n            } else {\n                console.log('❌ No valid stored auth found, clearing state');\n                this.clearAuth();\n            }\n        } catch (error) {\n            console.error('❌ Auth initialization error:', error);\n            this.updateState({\n                isLoading: false,\n                error: sanitizeError(error)\n            });\n            this.clearAuth();\n        }\n    }\n    // Login with comprehensive error handling\n    async login(credentials) {\n        this.updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            var _credentials_email, _credentials_password;\n            // Validate credentials\n            if (!((_credentials_email = credentials.email) === null || _credentials_email === void 0 ? void 0 : _credentials_email.trim()) || !((_credentials_password = credentials.password) === null || _credentials_password === void 0 ? void 0 : _credentials_password.trim())) {\n                throw new Error('Email and password are required');\n            }\n            const response = await apiRequest('/admin/login', {\n                method: 'POST',\n                body: JSON.stringify(credentials)\n            });\n            if (response.success && response.token && response.admin) {\n                console.log('✅ Login successful, storing auth data');\n                console.log('Token:', response.token.substring(0, 30) + '...');\n                console.log('Admin:', response.admin.email, response.admin.role);\n                // Store auth data\n                storage.set(TOKEN_KEY, response.token);\n                storage.set(ADMIN_KEY, JSON.stringify(response.admin));\n                console.log('✅ Auth data stored, updating state');\n                this.updateState({\n                    isAuthenticated: true,\n                    admin: response.admin,\n                    token: response.token,\n                    isLoading: false,\n                    error: null\n                });\n                console.log('✅ Auth state updated:', {\n                    isAuthenticated: true,\n                    hasAdmin: !!response.admin,\n                    hasToken: !!response.token\n                });\n            } else {\n                throw new Error(response.message || 'Login failed');\n            }\n            return response;\n        } catch (error) {\n            const errorMessage = sanitizeError(error);\n            this.updateState({\n                isLoading: false,\n                error: errorMessage\n            });\n            throw new Error(errorMessage);\n        }\n    }\n    // Logout with cleanup\n    async logout() {\n        this.updateState({\n            isLoading: true\n        });\n        try {\n            // Attempt to notify backend\n            try {\n                await apiRequest('/admin/logout', {\n                    method: 'POST'\n                });\n            } catch (e) {\n            // Silent fail - logout locally even if backend fails\n            }\n        } finally{\n            this.clearAuth();\n        }\n    }\n    // Verify token validity\n    async verifyToken() {\n        try {\n            const token = storage.get(TOKEN_KEY);\n            if (!token || isTokenExpired(token)) {\n                return false;\n            }\n            const response = await apiRequest('/admin/verify-token');\n            if (response.success && response.admin) {\n                // Update admin data\n                storage.set(ADMIN_KEY, JSON.stringify(response.admin));\n                this.updateState({\n                    admin: response.admin\n                });\n                return true;\n            }\n            return false;\n        } catch (e) {\n            return false;\n        }\n    }\n    // Clear authentication data\n    clearAuth() {\n        storage.remove(TOKEN_KEY);\n        storage.remove(ADMIN_KEY);\n        this.updateState({\n            isAuthenticated: false,\n            admin: null,\n            token: null,\n            isLoading: false,\n            error: null\n        });\n    }\n    // Get current auth state\n    getState() {\n        return {\n            ...this.authState\n        };\n    }\n    // Check if user has specific permission\n    hasPermission(resource, action) {\n        var _this_authState_admin;\n        if (!((_this_authState_admin = this.authState.admin) === null || _this_authState_admin === void 0 ? void 0 : _this_authState_admin.permissions)) return false;\n        const resourcePerms = this.authState.admin.permissions[resource];\n        if (!resourcePerms) return false;\n        return resourcePerms[action] === true;\n    }\n    // Check if user has role\n    hasRole(role) {\n        var _this_authState_admin;\n        return ((_this_authState_admin = this.authState.admin) === null || _this_authState_admin === void 0 ? void 0 : _this_authState_admin.role) === role;\n    }\n    // Get auth token\n    getToken() {\n        return this.authState.token;\n    }\n    // Get admin user\n    getAdmin() {\n        return this.authState.admin;\n    }\n    constructor(){\n        this.authState = {\n            isAuthenticated: false,\n            admin: null,\n            token: null,\n            isLoading: false,\n            error: null\n        };\n        this.listeners = [];\n    }\n}\n// Export singleton instance\nconst adminAuth = AdminAuthService.getInstance();\n// React hook for auth state\nconst useAdminAuth = ()=>{\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0___default().useState(adminAuth.getState());\n    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect({\n        \"useAdminAuth.useEffect\": ()=>{\n            const unsubscribe = adminAuth.subscribe(setState);\n            adminAuth.initialize(); // Initialize on first use\n            return unsubscribe;\n        }\n    }[\"useAdminAuth.useEffect\"], []);\n    return {\n        ...state,\n        login: adminAuth.login.bind(adminAuth),\n        logout: adminAuth.logout.bind(adminAuth),\n        hasPermission: adminAuth.hasPermission.bind(adminAuth),\n        hasRole: adminAuth.hasRole.bind(adminAuth)\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/adminAuth.ts\n"));

/***/ })

});