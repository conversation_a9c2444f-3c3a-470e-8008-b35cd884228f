{"name": "kareem", "version": "2.6.3", "description": "Next-generation take on pre/post function hooks", "main": "index.js", "scripts": {"lint": "eslint .", "test": "mocha ./test/*", "test-coverage": "nyc --reporter lcov mocha ./test/*", "docs": "node ./docs.js"}, "repository": {"type": "git", "url": "git://github.com/mongoosejs/kareem.git"}, "devDependencies": {"acquit": "1.x", "acquit-ignore": "0.2.x", "eslint": "8.20.0", "mocha": "9.2.0", "nyc": "15.1.0"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "Apache-2.0", "engines": {"node": ">=12.0.0"}}