"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/services/adminAuth.ts":
/*!***********************************!*\
  !*** ./src/services/adminAuth.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminAuthService: () => (/* binding */ AdminAuthService),\n/* harmony export */   adminAuth: () => (/* binding */ adminAuth),\n/* harmony export */   useAdminAuth: () => (/* binding */ useAdminAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api */ \"(app-pages-browser)/./src/services/api.ts\");\n/**\n * Professional Admin Authentication Service\n * Handles all admin authentication operations with comprehensive error handling,\n * fallbacks, and professional API architecture\n */ \n\n// Configuration\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nconst TOKEN_KEY = 'adminToken';\nconst ADMIN_KEY = 'adminUser';\nconst MAX_RETRY_ATTEMPTS = 3;\nconst RETRY_DELAY = 1000; // 1 second\n// Utility functions\nconst sleep = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\nconst isTokenExpired = (token)=>{\n    try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return Date.now() >= payload.exp * 1000;\n    } catch (e) {\n        return true;\n    }\n};\nconst sanitizeError = (error)=>{\n    if (error instanceof _api__WEBPACK_IMPORTED_MODULE_1__.ApiError) {\n        return error.message;\n    }\n    if (error && typeof error === 'object' && 'message' in error) {\n        return error.message;\n    }\n    if (typeof error === 'string') {\n        return error;\n    }\n    return 'An unexpected error occurred';\n};\n// Storage utilities with fallbacks\nconst storage = {\n    get: (key)=>{\n        try {\n            if (false) {}\n            return localStorage.getItem(key) || sessionStorage.getItem(key);\n        } catch (e) {\n            return null;\n        }\n    },\n    set: (key, value)=>{\n        try {\n            if (false) {}\n            localStorage.setItem(key, value);\n            // Fallback to sessionStorage if localStorage fails\n            try {\n                localStorage.setItem(key, value);\n            } catch (e) {\n                sessionStorage.setItem(key, value);\n            }\n        } catch (e) {\n        // Silent fail if both storage methods fail\n        }\n    },\n    remove: (key)=>{\n        try {\n            if (false) {}\n            localStorage.removeItem(key);\n            sessionStorage.removeItem(key);\n        } catch (e) {\n        // Silent fail\n        }\n    }\n};\n// API request utility with retry logic\nconst apiRequest = async function(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, retryCount = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    const defaultHeaders = {\n        'Content-Type': 'application/json',\n        ...options.headers\n    };\n    // Add auth token if available\n    const token = storage.get(TOKEN_KEY);\n    if (token && !isTokenExpired(token)) {\n        defaultHeaders['Authorization'] = \"Bearer \".concat(token);\n    }\n    try {\n        const response = await fetch(url, {\n            ...options,\n            headers: defaultHeaders\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            throw new _api__WEBPACK_IMPORTED_MODULE_1__.ApiError(data.message || \"HTTP \".concat(response.status), response.status, data);\n        }\n        return data;\n    } catch (error) {\n        // Retry logic for network errors\n        if (retryCount < MAX_RETRY_ATTEMPTS && (error instanceof TypeError || // Network error\n        error instanceof _api__WEBPACK_IMPORTED_MODULE_1__.ApiError && error.status >= 500)) {\n            await sleep(RETRY_DELAY * Math.pow(2, retryCount)); // Exponential backoff\n            return apiRequest(endpoint, options, retryCount + 1);\n        }\n        throw error;\n    }\n};\n// Admin Authentication Service\nclass AdminAuthService {\n    static getInstance() {\n        if (!AdminAuthService.instance) {\n            AdminAuthService.instance = new AdminAuthService();\n        }\n        return AdminAuthService.instance;\n    }\n    // Subscribe to auth state changes\n    subscribe(listener) {\n        this.listeners.push(listener);\n        return ()=>{\n            this.listeners = this.listeners.filter((l)=>l !== listener);\n        };\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(this.authState));\n    }\n    updateState(updates) {\n        this.authState = {\n            ...this.authState,\n            ...updates\n        };\n        this.notifyListeners();\n    }\n    // Initialize auth state from storage\n    async initialize() {\n        if (this.isInitialized) {\n            console.log('⚠️ Auth service already initialized, skipping...');\n            return;\n        }\n        console.log('🔄 Initializing auth service...');\n        this.isInitialized = true;\n        this.updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            const token = storage.get(TOKEN_KEY);\n            const adminData = storage.get(ADMIN_KEY);\n            console.log('📦 Storage check:', {\n                hasToken: !!token,\n                hasAdminData: !!adminData,\n                tokenExpired: token ? isTokenExpired(token) : 'no token'\n            });\n            if (token && adminData && !isTokenExpired(token)) {\n                const admin = JSON.parse(adminData);\n                console.log('✅ Valid stored auth found, setting authenticated state');\n                console.log('Admin:', admin.email, admin.role);\n                // Set authenticated state immediately with stored data\n                this.updateState({\n                    isAuthenticated: true,\n                    admin,\n                    token,\n                    isLoading: false,\n                    error: null\n                });\n                console.log('✅ Auth state set, verifying token in background...');\n                // Verify token with backend in background (don't block UI)\n                this.verifyToken().then((isValid)=>{\n                    console.log('🔍 Token verification result:', isValid);\n                    if (!isValid) {\n                        // Only clear auth if token verification explicitly fails\n                        // This prevents network issues from logging out users\n                        console.warn('❌ Token verification failed, clearing auth');\n                        this.clearAuth();\n                    } else {\n                        console.log('✅ Token verification successful');\n                    }\n                }).catch((error)=>{\n                    console.warn('⚠️ Token verification error (keeping auth):', error);\n                // Don't clear auth on network errors - keep user logged in\n                });\n            } else {\n                console.log('❌ No valid stored auth found, clearing state');\n                this.clearAuth();\n            }\n        } catch (error) {\n            console.error('❌ Auth initialization error:', error);\n            this.updateState({\n                isLoading: false,\n                error: sanitizeError(error)\n            });\n            this.clearAuth();\n        }\n    }\n    // Login with comprehensive error handling\n    async login(credentials) {\n        this.updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            var _credentials_email, _credentials_password;\n            // Validate credentials\n            if (!((_credentials_email = credentials.email) === null || _credentials_email === void 0 ? void 0 : _credentials_email.trim()) || !((_credentials_password = credentials.password) === null || _credentials_password === void 0 ? void 0 : _credentials_password.trim())) {\n                throw new Error('Email and password are required');\n            }\n            const response = await apiRequest('/admin/login', {\n                method: 'POST',\n                body: JSON.stringify(credentials)\n            });\n            if (response.success && response.token && response.admin) {\n                console.log('✅ Login successful, storing auth data');\n                console.log('Token:', response.token.substring(0, 30) + '...');\n                console.log('Admin:', response.admin.email, response.admin.role);\n                // Store auth data\n                storage.set(TOKEN_KEY, response.token);\n                storage.set(ADMIN_KEY, JSON.stringify(response.admin));\n                console.log('✅ Auth data stored, updating state');\n                this.updateState({\n                    isAuthenticated: true,\n                    admin: response.admin,\n                    token: response.token,\n                    isLoading: false,\n                    error: null\n                });\n                console.log('✅ Auth state updated:', {\n                    isAuthenticated: true,\n                    hasAdmin: !!response.admin,\n                    hasToken: !!response.token\n                });\n            } else {\n                throw new Error(response.message || 'Login failed');\n            }\n            return response;\n        } catch (error) {\n            const errorMessage = sanitizeError(error);\n            this.updateState({\n                isLoading: false,\n                error: errorMessage\n            });\n            throw new Error(errorMessage);\n        }\n    }\n    // Logout with cleanup\n    async logout() {\n        this.updateState({\n            isLoading: true\n        });\n        try {\n            // Attempt to notify backend\n            try {\n                await apiRequest('/admin/logout', {\n                    method: 'POST'\n                });\n            } catch (e) {\n            // Silent fail - logout locally even if backend fails\n            }\n        } finally{\n            this.clearAuth();\n        }\n    }\n    // Verify token validity\n    async verifyToken() {\n        try {\n            const token = storage.get(TOKEN_KEY);\n            if (!token || isTokenExpired(token)) {\n                return false;\n            }\n            const response = await apiRequest('/admin/verify-token');\n            if (response.success && response.admin) {\n                // Update admin data\n                storage.set(ADMIN_KEY, JSON.stringify(response.admin));\n                this.updateState({\n                    admin: response.admin\n                });\n                return true;\n            }\n            return false;\n        } catch (e) {\n            return false;\n        }\n    }\n    // Clear authentication data\n    clearAuth() {\n        storage.remove(TOKEN_KEY);\n        storage.remove(ADMIN_KEY);\n        this.updateState({\n            isAuthenticated: false,\n            admin: null,\n            token: null,\n            isLoading: false,\n            error: null\n        });\n    }\n    // Get current auth state\n    getState() {\n        return {\n            ...this.authState\n        };\n    }\n    // Check if user has specific permission\n    hasPermission(resource, action) {\n        var _this_authState_admin;\n        if (!((_this_authState_admin = this.authState.admin) === null || _this_authState_admin === void 0 ? void 0 : _this_authState_admin.permissions)) return false;\n        const resourcePerms = this.authState.admin.permissions[resource];\n        if (!resourcePerms) return false;\n        return resourcePerms[action] === true;\n    }\n    // Check if user has role\n    hasRole(role) {\n        var _this_authState_admin;\n        return ((_this_authState_admin = this.authState.admin) === null || _this_authState_admin === void 0 ? void 0 : _this_authState_admin.role) === role;\n    }\n    // Get auth token\n    getToken() {\n        return this.authState.token;\n    }\n    // Get admin user\n    getAdmin() {\n        return this.authState.admin;\n    }\n    constructor(){\n        this.authState = {\n            isAuthenticated: false,\n            admin: null,\n            token: null,\n            isLoading: false,\n            error: null\n        };\n        this.listeners = [];\n        this.isInitialized = false;\n    }\n}\n// Export singleton instance\nconst adminAuth = AdminAuthService.getInstance();\n// React hook for auth state\nconst useAdminAuth = ()=>{\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0___default().useState(adminAuth.getState());\n    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect({\n        \"useAdminAuth.useEffect\": ()=>{\n            const unsubscribe = adminAuth.subscribe(setState);\n            adminAuth.initialize(); // Initialize on first use\n            return unsubscribe;\n        }\n    }[\"useAdminAuth.useEffect\"], []);\n    return {\n        ...state,\n        login: adminAuth.login.bind(adminAuth),\n        logout: adminAuth.logout.bind(adminAuth),\n        hasPermission: adminAuth.hasPermission.bind(adminAuth),\n        hasRole: adminAuth.hasRole.bind(adminAuth)\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/adminAuth.ts\n"));

/***/ })

});