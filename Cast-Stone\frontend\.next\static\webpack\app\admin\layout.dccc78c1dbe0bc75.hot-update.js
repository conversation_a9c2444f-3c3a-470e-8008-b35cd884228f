"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/app/admin/layout.tsx":
/*!**********************************!*\
  !*** ./src/app/admin/layout.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/admin/AdminSidebar */ \"(app-pages-browser)/./src/components/admin/AdminSidebar.tsx\");\n/* harmony import */ var _components_admin_AdminHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/admin/AdminHeader */ \"(app-pages-browser)/./src/components/admin/AdminHeader.tsx\");\n/* harmony import */ var _layout_module_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./layout.module.css */ \"(app-pages-browser)/./src/app/admin/layout.module.css\");\n/* harmony import */ var _layout_module_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_layout_module_css__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AdminLayout(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isLoading, setIsLoading] = useState(true);\n    const [isAuthenticated, setIsAuthenticated] = useState(false);\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/admin/login',\n        '/admin/change-password'\n    ];\n    const isPublicRoute = publicRoutes.includes(pathname);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminLayout.useEffect\": ()=>{\n            const checkAuth = {\n                \"AdminLayout.useEffect.checkAuth\": async ()=>{\n                    console.log('Checking auth for path:', pathname);\n                    console.log('Is public route:', isPublicRoute);\n                    if (isPublicRoute) {\n                        setIsLoading(false);\n                        return;\n                    }\n                    const token = localStorage.getItem('adminToken');\n                    console.log('Token found:', !!token);\n                    if (!token) {\n                        console.log('No token, redirecting to login');\n                        router.push('/admin/login');\n                        return;\n                    }\n                    try {\n                        const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n                        console.log('Verifying token with:', \"\".concat(API_BASE_URL, \"/admin/verify-token\"));\n                        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/verify-token\"), {\n                            headers: {\n                                'Authorization': \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('Token verification response:', response.status);\n                        if (response.ok) {\n                            const result = await response.json();\n                            console.log('Token verification result:', result);\n                            setIsAuthenticated(true);\n                        } else {\n                            console.log('Token verification failed, removing token');\n                            localStorage.removeItem('adminToken');\n                            router.push('/admin/login');\n                        }\n                    } catch (error) {\n                        console.error('Auth check failed:', error);\n                        localStorage.removeItem('adminToken');\n                        router.push('/admin/login');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AdminLayout.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AdminLayout.useEffect\"], [\n        pathname,\n        router,\n        isPublicRoute\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_5___default().loadingContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_5___default().spinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    // Render public routes without admin layout\n    if (isPublicRoute) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // Render admin dashboard layout for authenticated routes\n    if (isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdminProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_5___default().adminLayout),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_5___default().mainContent),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_5___default().content),\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_s(AdminLayout, \"YzRRBITeHCZr4erkwIOd/LUeNOQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/layout.tsx\n"));

/***/ })

});