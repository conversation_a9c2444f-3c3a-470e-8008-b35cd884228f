'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import AdminSidebar from '../../components/admin/AdminSidebar';
import AdminHeader from '../../components/admin/AdminHeader';
import { useAdminAuth } from '../../services/adminAuth';
import styles from './layout.module.css';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, isLoading, admin } = useAdminAuth();

  // Debug logging
  console.log('AdminLayout render:', {
    pathname,
    isAuthenticated,
    isLoading,
    hasAdmin: !!admin
  });

  // Public routes that don't require authentication
  const publicRoutes = ['/admin/login', '/admin/change-password'];
  const isPublicRoute = publicRoutes.includes(pathname);

  useEffect(() => {
    // Don't do anything while loading
    if (isLoading) return;

    // Redirect to login if not authenticated and not on public route
    if (!isAuthenticated && !isPublicRoute) {
      console.log('Redirecting to login - not authenticated');
      router.push('/admin/login');
      return;
    }

    // Redirect to dashboard if authenticated and on login page
    if (isAuthenticated && pathname === '/admin/login') {
      console.log('Redirecting to dashboard - already authenticated');
      router.push('/admin/dashboard');
      return;
    }
  }, [isAuthenticated, isLoading, isPublicRoute, pathname, router]);

  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading admin...</p>
      </div>
    );
  }

  // Render public routes without admin layout
  if (isPublicRoute) {
    return <>{children}</>;
  }

  // Render admin dashboard layout for authenticated routes
  if (isAuthenticated && admin) {
    return (
      <div className={styles.adminLayout}>
        <AdminSidebar />
        <div className={styles.mainContent}>
          <AdminHeader />
          <main className={styles.content}>
            {children}
          </main>
        </div>
      </div>
    );
  }

  // Show loading or redirect (handled by useEffect)
  return (
    <div className={styles.loadingContainer}>
      <div className={styles.spinner}></div>
      <p>Authenticating...</p>
    </div>
  );
}
