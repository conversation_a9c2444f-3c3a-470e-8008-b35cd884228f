'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import AdminSidebar from '../../components/admin/AdminSidebar';
import AdminHeader from '../../components/admin/AdminHeader';
import { useAdminAuth } from '../../services/adminAuth';
import styles from './layout.module.css';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, isLoading, admin } = useAdminAuth();

  // Debug logging
  console.log('AdminLayout render:', {
    pathname,
    isAuthenticated,
    isLoading,
    hasAdmin: !!admin
  });

  // Public routes that don't require authentication
  const publicRoutes = ['/admin/login', '/admin/change-password'];
  const isPublicRoute = publicRoutes.includes(pathname);

  useEffect(() => {
    // Don't do anything while loading
    if (isLoading) {
      console.log('AdminLayout: Still loading, waiting...');
      return;
    }

    console.log('AdminLayout: Auth check -', {
      isAuthenticated,
      pathname,
      isPublicRoute,
      hasAdmin: !!admin
    });

    // Redirect to login if not authenticated and not on public route
    if (!isAuthenticated && !isPublicRoute) {
      console.log('AdminLayout: Redirecting to login - not authenticated');
      const redirectUrl = `/admin/login${pathname !== '/admin' ? `?redirect=${encodeURIComponent(pathname)}` : ''}`;
      router.push(redirectUrl);
      return;
    }

    // Redirect to dashboard if authenticated and on login page
    if (isAuthenticated && pathname === '/admin/login') {
      console.log('AdminLayout: Redirecting to dashboard - already authenticated');
      // Check for redirect parameter
      const urlParams = new URLSearchParams(window.location.search);
      const redirectTo = urlParams.get('redirect') || '/admin/dashboard';
      router.push(redirectTo);
      return;
    }
  }, [isAuthenticated, isLoading, isPublicRoute, pathname, router, admin]);

  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading admin...</p>
      </div>
    );
  }

  // Render public routes without admin layout
  if (isPublicRoute) {
    return <>{children}</>;
  }

  // Render admin dashboard layout for authenticated routes
  if (isAuthenticated && admin) {
    return (
      <div className={styles.adminLayout}>
        <AdminSidebar />
        <div className={styles.mainContent}>
          <AdminHeader />
          <main className={styles.content}>
            {children}
          </main>
        </div>
      </div>
    );
  }

  // Show loading or redirect (handled by useEffect)
  return (
    <div className={styles.loadingContainer}>
      <div className={styles.spinner}></div>
      <p>Authenticating...</p>
    </div>
  );
}
