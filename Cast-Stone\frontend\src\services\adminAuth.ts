/**
 * Professional Admin Authentication Service
 * Handles all admin authentication operations with comprehensive error handling,
 * fallbacks, and professional API architecture
 */

import React from 'react';
import { ApiError } from './api';

// Types
export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'super_admin' | 'admin' | 'moderator';
  permissions: {
    products: { create: boolean; read: boolean; update: boolean; delete: boolean };
    orders: { create: boolean; read: boolean; update: boolean; delete: boolean };
    users: { create: boolean; read: boolean; update: boolean; delete: boolean };
    admins: { create: boolean; read: boolean; update: boolean; delete: boolean };
    analytics: { read: boolean };
    settings: { read: boolean; update: boolean };
  };
  mustChangePassword: boolean;
  lastLogin: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  token?: string;
  admin?: AdminUser;
}

export interface AuthState {
  isAuthenticated: boolean;
  admin: AdminUser | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

// Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
const TOKEN_KEY = 'adminToken';
const ADMIN_KEY = 'adminUser';
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 1000; // 1 second

// Utility functions
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return Date.now() >= payload.exp * 1000;
  } catch {
    return true;
  }
};

const sanitizeError = (error: any): string => {
  if (error instanceof ApiError) {
    return error.message;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'An unexpected error occurred';
};

// Storage utilities with fallbacks
const storage = {
  get: (key: string): string | null => {
    try {
      if (typeof window === 'undefined') return null;
      return localStorage.getItem(key) || sessionStorage.getItem(key);
    } catch {
      return null;
    }
  },
  
  set: (key: string, value: string): void => {
    try {
      if (typeof window === 'undefined') return;
      localStorage.setItem(key, value);
      // Fallback to sessionStorage if localStorage fails
      try {
        localStorage.setItem(key, value);
      } catch {
        sessionStorage.setItem(key, value);
      }
    } catch {
      // Silent fail if both storage methods fail
    }
  },
  
  remove: (key: string): void => {
    try {
      if (typeof window === 'undefined') return;
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    } catch {
      // Silent fail
    }
  }
};

// API request utility with retry logic
const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {},
  retryCount = 0
): Promise<T> => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
    ...options.headers,
  };
  
  // Add auth token if available
  const token = storage.get(TOKEN_KEY);
  if (token && !isTokenExpired(token)) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  }
  
  try {
    const response = await fetch(url, {
      ...options,
      headers: defaultHeaders,
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new ApiError(
        data.message || `HTTP ${response.status}`,
        response.status,
        data
      );
    }
    
    return data;
  } catch (error) {
    // Retry logic for network errors
    if (retryCount < MAX_RETRY_ATTEMPTS && 
        (error instanceof TypeError || // Network error
         (error instanceof ApiError && error.status >= 500))) {
      
      await sleep(RETRY_DELAY * Math.pow(2, retryCount)); // Exponential backoff
      return apiRequest<T>(endpoint, options, retryCount + 1);
    }
    
    throw error;
  }
};

// Admin Authentication Service
export class AdminAuthService {
  private static instance: AdminAuthService;
  private authState: AuthState = {
    isAuthenticated: false,
    admin: null,
    token: null,
    isLoading: false,
    error: null,
  };
  
  private listeners: Array<(state: AuthState) => void> = [];
  
  static getInstance(): AdminAuthService {
    if (!AdminAuthService.instance) {
      AdminAuthService.instance = new AdminAuthService();
    }
    return AdminAuthService.instance;
  }
  
  // Subscribe to auth state changes
  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }
  
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.authState));
  }
  
  private updateState(updates: Partial<AuthState>): void {
    this.authState = { ...this.authState, ...updates };
    this.notifyListeners();
  }
  
  // Initialize auth state from storage
  async initialize(): Promise<void> {
    this.updateState({ isLoading: true, error: null });
    
    try {
      const token = storage.get(TOKEN_KEY);
      const adminData = storage.get(ADMIN_KEY);
      
      if (token && adminData && !isTokenExpired(token)) {
        const admin = JSON.parse(adminData);
        
        // Verify token with backend
        try {
          await this.verifyToken();
          this.updateState({
            isAuthenticated: true,
            admin,
            token,
            isLoading: false,
            error: null,
          });
        } catch {
          // Token invalid, clear storage
          this.clearAuth();
        }
      } else {
        this.clearAuth();
      }
    } catch (error) {
      this.updateState({
        isLoading: false,
        error: sanitizeError(error),
      });
      this.clearAuth();
    }
  }
  
  // Login with comprehensive error handling
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    this.updateState({ isLoading: true, error: null });
    
    try {
      // Validate credentials
      if (!credentials.email?.trim() || !credentials.password?.trim()) {
        throw new Error('Email and password are required');
      }
      
      const response = await apiRequest<LoginResponse>('/admin/login', {
        method: 'POST',
        body: JSON.stringify(credentials),
      });
      
      if (response.success && response.token && response.admin) {
        // Store auth data
        storage.set(TOKEN_KEY, response.token);
        storage.set(ADMIN_KEY, JSON.stringify(response.admin));
        
        this.updateState({
          isAuthenticated: true,
          admin: response.admin,
          token: response.token,
          isLoading: false,
          error: null,
        });
      } else {
        throw new Error(response.message || 'Login failed');
      }
      
      return response;
    } catch (error) {
      const errorMessage = sanitizeError(error);
      this.updateState({
        isLoading: false,
        error: errorMessage,
      });
      throw new Error(errorMessage);
    }
  }
  
  // Logout with cleanup
  async logout(): Promise<void> {
    this.updateState({ isLoading: true });
    
    try {
      // Attempt to notify backend
      try {
        await apiRequest('/admin/logout', { method: 'POST' });
      } catch {
        // Silent fail - logout locally even if backend fails
      }
    } finally {
      this.clearAuth();
    }
  }
  
  // Verify token validity
  async verifyToken(): Promise<boolean> {
    try {
      const token = storage.get(TOKEN_KEY);
      if (!token || isTokenExpired(token)) {
        return false;
      }
      
      const response = await apiRequest<{ success: boolean; admin: AdminUser }>('/admin/verify-token');
      
      if (response.success && response.admin) {
        // Update admin data
        storage.set(ADMIN_KEY, JSON.stringify(response.admin));
        this.updateState({ admin: response.admin });
        return true;
      }
      
      return false;
    } catch {
      return false;
    }
  }
  
  // Clear authentication data
  private clearAuth(): void {
    storage.remove(TOKEN_KEY);
    storage.remove(ADMIN_KEY);
    
    this.updateState({
      isAuthenticated: false,
      admin: null,
      token: null,
      isLoading: false,
      error: null,
    });
  }
  
  // Get current auth state
  getState(): AuthState {
    return { ...this.authState };
  }
  
  // Check if user has specific permission
  hasPermission(resource: string, action: string): boolean {
    if (!this.authState.admin?.permissions) return false;
    
    const resourcePerms = this.authState.admin.permissions[resource as keyof typeof this.authState.admin.permissions];
    if (!resourcePerms) return false;
    
    return resourcePerms[action as keyof typeof resourcePerms] === true;
  }
  
  // Check if user has role
  hasRole(role: string): boolean {
    return this.authState.admin?.role === role;
  }
  
  // Get auth token
  getToken(): string | null {
    return this.authState.token;
  }
  
  // Get admin user
  getAdmin(): AdminUser | null {
    return this.authState.admin;
  }
}

// Export singleton instance
export const adminAuth = AdminAuthService.getInstance();

// React hook for auth state
export const useAdminAuth = () => {
  const [state, setState] = React.useState(adminAuth.getState());
  
  React.useEffect(() => {
    const unsubscribe = adminAuth.subscribe(setState);
    adminAuth.initialize(); // Initialize on first use
    return unsubscribe;
  }, []);
  
  return {
    ...state,
    login: adminAuth.login.bind(adminAuth),
    logout: adminAuth.logout.bind(adminAuth),
    hasPermission: adminAuth.hasPermission.bind(adminAuth),
    hasRole: adminAuth.hasRole.bind(adminAuth),
  };
};
