/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/admin/dashboard/page.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
.page_dashboard__JUU1N {
  padding: 0;
}

.page_loading__7Viap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 20px;
}

.page_spinner__I__V4 {
  width: 40px;
  height: 40px;
  border: 4px solid var(--cart-border);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: page_spin__g1XfS 1s linear infinite;
}

.page_loading__7Viap p {
  color: var(--cart-text-secondary);
  font-size: 1.1rem;
}

@keyframes page_spin__g1XfS {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.page_header__eUZi2 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.page_title__QVaAu {
  font-size: 2rem;
  font-weight: 700;
  color: var(--cart-text-primary);
  margin: 0 0 8px 0;
}

.page_subtitle__D5J7z {
  color: var(--cart-text-secondary);
  margin: 0;
  font-size: 1rem;
}

.page_headerActions__9RQTm {
  display: flex;
  gap: 12px;
}

.page_refreshButton__hTf6w {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: white;
  border: 1px solid var(--cart-border);
  border-radius: 8px;
  color: var(--cart-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.page_refreshButton__hTf6w:hover {
  background: var(--background-light);
  color: var(--cart-text-primary);
}

.page_refreshButton__hTf6w svg {
  width: 16px;
  height: 16px;
}

.page_settingsButton__DFLOw {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: white;
  border: 1px solid var(--cart-border);
  border-radius: 8px;
  color: var(--cart-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.page_settingsButton__DFLOw:hover {
  background: var(--background-light);
  color: var(--cart-text-primary);
}

.page_settingsButton__DFLOw svg {
  width: 16px;
  height: 16px;
}

.page_logoutButton__yKYyp {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #ef4444;
  border: 1px solid #dc2626;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.page_logoutButton__yKYyp:hover {
  background: #dc2626;
  border-color: #b91c1c;
}

.page_logoutButton__yKYyp svg {
  width: 16px;
  height: 16px;
}

.page_statsGrid__iYD0I {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.page_statCard__H_kQ_ {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  transition: transform 0.2s ease;
}

.page_statCard__H_kQ_:hover {
  transform: translateY(-2px);
}

.page_statCard__H_kQ_.page_blue__uKDzU {
  border-left-color: #3b82f6;
}

.page_statCard__H_kQ_.page_green__lbETj {
  border-left-color: #10b981;
}

.page_statCard__H_kQ_.page_purple__QLvev {
  border-left-color: #8b5cf6;
}

.page_statCard__H_kQ_.page_orange__OcpmL {
  border-left-color: #f59e0b;
}

.page_statHeader__y79Kx {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page_statIcon___RLsa {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.page_blue__uKDzU .page_statIcon___RLsa {
  background: #3b82f6;
}

.page_green__lbETj .page_statIcon___RLsa {
  background: #10b981;
}

.page_purple__QLvev .page_statIcon___RLsa {
  background: #8b5cf6;
}

.page_orange__OcpmL .page_statIcon___RLsa {
  background: #f59e0b;
}

.page_statIcon___RLsa svg {
  width: 24px;
  height: 24px;
}

.page_statTrend__5j5Mh {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  color: #10b981;
}

.page_statTrend__5j5Mh svg {
  width: 14px;
  height: 14px;
}

.page_statContent__XmS1G {
  text-align: left;
}

.page_statValue__z4YkS {
  font-size: 2rem;
  font-weight: 700;
  color: var(--cart-text-primary);
  margin: 0 0 4px 0;
}

.page_statTitle__reEfB {
  color: var(--cart-text-secondary);
  margin: 0;
  font-size: 14px;
}

.page_contentGrid__Dqqzm {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.page_card__mgJny {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.page_cardHeader__wrI_x {
  padding: 24px 24px 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page_cardTitle__dX1uq {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0;
}

.page_viewAllButton__6yWuY {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: none;
  border: 1px solid var(--cart-border);
  border-radius: 6px;
  color: var(--cart-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.page_viewAllButton__6yWuY:hover {
  background: var(--background-light);
  color: var(--cart-text-primary);
}

.page_viewAllButton__6yWuY svg {
  width: 14px;
  height: 14px;
}

.page_cardContent__fUF0d {
  padding: 0 24px 24px 24px;
}

.page_table__M7bHO {
  width: 100%;
}

.page_tableHeader__lpJaZ {
  display: grid;
  grid-template-columns: 1fr 1fr 100px 100px 100px;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid var(--cart-border);
  font-size: 12px;
  font-weight: 600;
  color: var(--cart-text-secondary);
  text-transform: uppercase;
}

.page_tableRow__EPBPi {
  display: grid;
  grid-template-columns: 1fr 1fr 100px 100px 100px;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid var(--cart-border);
  font-size: 14px;
  align-items: center;
}

.page_tableRow__EPBPi:last-child {
  border-bottom: none;
}

.page_orderNumber__mo_6_ {
  font-weight: 600;
  color: var(--primary-color);
}

.page_orderTotal__CRIwP {
  font-weight: 600;
  color: var(--cart-text-primary);
}

.page_status__tmZAO {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  text-align: center;
}

.page_status__tmZAO.page_confirmed__H_onY {
  background: #dcfce7;
  color: #166534;
}

.page_status__tmZAO.page_processing__CW6mC {
  background: #fef3c7;
  color: #92400e;
}

.page_status__tmZAO.page_shipped__wDhat {
  background: #dbeafe;
  color: #1e40af;
}

.page_status__tmZAO.page_delivered__iNp7_ {
  background: #dcfce7;
  color: #166534;
}

.page_status__tmZAO.page_canceled__JGFFn {
  background: #fee2e2;
  color: #991b1b;
}

.page_productList__X59Y0 {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.page_productItem___u_Hn {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
}

.page_productRank__IwrO7 {
  width: 32px;
  height: 32px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.page_productInfo__rooyY {
  flex: 1;
}

.page_productName__BZGz3 {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0 0 4px 0;
}

.page_productStats__9YHqI {
  font-size: 12px;
  color: var(--cart-text-secondary);
  margin: 0;
}

/* Mobile responsive */
@media (max-width: 1024px) {
  .page_contentGrid__Dqqzm {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page_header__eUZi2 {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .page_title__QVaAu {
    font-size: 1.5rem;
  }
  
  .page_statsGrid__iYD0I {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .page_statCard__H_kQ_ {
    padding: 20px;
  }
  
  .page_statValue__z4YkS {
    font-size: 1.5rem;
  }
  
  .page_tableHeader__lpJaZ,
  .page_tableRow__EPBPi {
    grid-template-columns: 1fr 80px 80px;
    gap: 12px;
  }
  
  .page_tableHeader__lpJaZ span:nth-child(2),
  .page_tableHeader__lpJaZ span:nth-child(5),
  .page_tableRow__EPBPi span:nth-child(2),
  .page_tableRow__EPBPi span:nth-child(5) {
    display: none;
  }
  
  .page_cardHeader__wrI_x {
    padding: 20px 20px 0 20px;
  }
  
  .page_cardContent__fUF0d {
    padding: 0 20px 20px 20px;
  }
}

