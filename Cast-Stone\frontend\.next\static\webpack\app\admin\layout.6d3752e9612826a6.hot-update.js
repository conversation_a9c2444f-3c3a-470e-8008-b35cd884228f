"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/services/adminAuth.ts":
/*!***********************************!*\
  !*** ./src/services/adminAuth.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminAuthService: () => (/* binding */ AdminAuthService),\n/* harmony export */   adminAuth: () => (/* binding */ adminAuth),\n/* harmony export */   useAdminAuth: () => (/* binding */ useAdminAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api */ \"(app-pages-browser)/./src/services/api.ts\");\n/**\n * Professional Admin Authentication Service\n * Handles all admin authentication operations with comprehensive error handling,\n * fallbacks, and professional API architecture\n */ \n\n// Configuration\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nconst TOKEN_KEY = 'adminToken';\nconst ADMIN_KEY = 'adminUser';\nconst MAX_RETRY_ATTEMPTS = 3;\nconst RETRY_DELAY = 1000; // 1 second\n// Utility functions\nconst sleep = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\nconst isTokenExpired = (token)=>{\n    try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        return Date.now() >= payload.exp * 1000;\n    } catch (e) {\n        return true;\n    }\n};\nconst sanitizeError = (error)=>{\n    if (error instanceof _api__WEBPACK_IMPORTED_MODULE_1__.ApiError) {\n        return error.message;\n    }\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        return error.message;\n    }\n    if (typeof error === 'string') {\n        return error;\n    }\n    return 'An unexpected error occurred';\n};\n// Storage utilities with fallbacks\nconst storage = {\n    get: (key)=>{\n        try {\n            if (false) {}\n            return localStorage.getItem(key) || sessionStorage.getItem(key);\n        } catch (e) {\n            return null;\n        }\n    },\n    set: (key, value)=>{\n        try {\n            if (false) {}\n            localStorage.setItem(key, value);\n            // Fallback to sessionStorage if localStorage fails\n            try {\n                localStorage.setItem(key, value);\n            } catch (e) {\n                sessionStorage.setItem(key, value);\n            }\n        } catch (e) {\n        // Silent fail if both storage methods fail\n        }\n    },\n    remove: (key)=>{\n        try {\n            if (false) {}\n            localStorage.removeItem(key);\n            sessionStorage.removeItem(key);\n        } catch (e) {\n        // Silent fail\n        }\n    }\n};\n// API request utility with retry logic\nconst apiRequest = async function(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, retryCount = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    const defaultHeaders = {\n        'Content-Type': 'application/json',\n        ...options.headers\n    };\n    // Add auth token if available\n    const token = storage.get(TOKEN_KEY);\n    if (token && !isTokenExpired(token)) {\n        defaultHeaders['Authorization'] = \"Bearer \".concat(token);\n    }\n    try {\n        const response = await fetch(url, {\n            ...options,\n            headers: defaultHeaders\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            throw new _api__WEBPACK_IMPORTED_MODULE_1__.ApiError(data.message || \"HTTP \".concat(response.status), response.status, data);\n        }\n        return data;\n    } catch (error) {\n        // Retry logic for network errors\n        if (retryCount < MAX_RETRY_ATTEMPTS && (error instanceof TypeError || // Network error\n        error instanceof _api__WEBPACK_IMPORTED_MODULE_1__.ApiError && error.status >= 500)) {\n            await sleep(RETRY_DELAY * Math.pow(2, retryCount)); // Exponential backoff\n            return apiRequest(endpoint, options, retryCount + 1);\n        }\n        throw error;\n    }\n};\n// Admin Authentication Service\nclass AdminAuthService {\n    static getInstance() {\n        if (!AdminAuthService.instance) {\n            AdminAuthService.instance = new AdminAuthService();\n        }\n        return AdminAuthService.instance;\n    }\n    // Subscribe to auth state changes\n    subscribe(listener) {\n        this.listeners.push(listener);\n        return ()=>{\n            this.listeners = this.listeners.filter((l)=>l !== listener);\n        };\n    }\n    notifyListeners() {\n        this.listeners.forEach((listener)=>listener(this.authState));\n    }\n    updateState(updates) {\n        this.authState = {\n            ...this.authState,\n            ...updates\n        };\n        this.notifyListeners();\n    }\n    // Initialize auth state from storage\n    async initialize() {\n        console.log('🔄 Initializing auth service...');\n        this.updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            const token = storage.get(TOKEN_KEY);\n            const adminData = storage.get(ADMIN_KEY);\n            console.log('📦 Storage check:', {\n                hasToken: !!token,\n                hasAdminData: !!adminData,\n                tokenExpired: token ? isTokenExpired(token) : 'no token'\n            });\n            if (token && adminData && !isTokenExpired(token)) {\n                const admin = JSON.parse(adminData);\n                console.log('✅ Valid stored auth found, setting authenticated state');\n                console.log('Admin:', admin.email, admin.role);\n                // Set authenticated state immediately with stored data\n                this.updateState({\n                    isAuthenticated: true,\n                    admin,\n                    token,\n                    isLoading: false,\n                    error: null\n                });\n                console.log('✅ Auth state set, verifying token in background...');\n                // Verify token with backend in background (don't block UI)\n                this.verifyToken().then((isValid)=>{\n                    console.log('🔍 Token verification result:', isValid);\n                    if (!isValid) {\n                        // Only clear auth if token verification explicitly fails\n                        // This prevents network issues from logging out users\n                        console.warn('❌ Token verification failed, clearing auth');\n                        this.clearAuth();\n                    } else {\n                        console.log('✅ Token verification successful');\n                    }\n                }).catch((error)=>{\n                    console.warn('⚠️ Token verification error (keeping auth):', error);\n                // Don't clear auth on network errors - keep user logged in\n                });\n            } else {\n                console.log('❌ No valid stored auth found, clearing state');\n                this.clearAuth();\n            }\n        } catch (error) {\n            console.error('❌ Auth initialization error:', error);\n            this.updateState({\n                isLoading: false,\n                error: sanitizeError(error)\n            });\n            this.clearAuth();\n        }\n    }\n    // Login with comprehensive error handling\n    async login(credentials) {\n        this.updateState({\n            isLoading: true,\n            error: null\n        });\n        try {\n            var _credentials_email, _credentials_password;\n            // Validate credentials\n            if (!((_credentials_email = credentials.email) === null || _credentials_email === void 0 ? void 0 : _credentials_email.trim()) || !((_credentials_password = credentials.password) === null || _credentials_password === void 0 ? void 0 : _credentials_password.trim())) {\n                throw new Error('Email and password are required');\n            }\n            const response = await apiRequest('/admin/login', {\n                method: 'POST',\n                body: JSON.stringify(credentials)\n            });\n            if (response.success && response.token && response.admin) {\n                console.log('✅ Login successful, storing auth data');\n                console.log('Token:', response.token.substring(0, 30) + '...');\n                console.log('Admin:', response.admin.email, response.admin.role);\n                // Store auth data\n                storage.set(TOKEN_KEY, response.token);\n                storage.set(ADMIN_KEY, JSON.stringify(response.admin));\n                console.log('✅ Auth data stored, updating state');\n                this.updateState({\n                    isAuthenticated: true,\n                    admin: response.admin,\n                    token: response.token,\n                    isLoading: false,\n                    error: null\n                });\n                console.log('✅ Auth state updated:', {\n                    isAuthenticated: true,\n                    hasAdmin: !!response.admin,\n                    hasToken: !!response.token\n                });\n            } else {\n                throw new Error(response.message || 'Login failed');\n            }\n            return response;\n        } catch (error) {\n            const errorMessage = sanitizeError(error);\n            this.updateState({\n                isLoading: false,\n                error: errorMessage\n            });\n            throw new Error(errorMessage);\n        }\n    }\n    // Logout with cleanup\n    async logout() {\n        this.updateState({\n            isLoading: true\n        });\n        try {\n            // Attempt to notify backend\n            try {\n                await apiRequest('/admin/logout', {\n                    method: 'POST'\n                });\n            } catch (e) {\n            // Silent fail - logout locally even if backend fails\n            }\n        } finally{\n            this.clearAuth();\n        }\n    }\n    // Verify token validity\n    async verifyToken() {\n        try {\n            const token = storage.get(TOKEN_KEY);\n            if (!token || isTokenExpired(token)) {\n                return false;\n            }\n            const response = await apiRequest('/admin/verify-token');\n            if (response.success && response.admin) {\n                // Update admin data\n                storage.set(ADMIN_KEY, JSON.stringify(response.admin));\n                this.updateState({\n                    admin: response.admin\n                });\n                return true;\n            }\n            return false;\n        } catch (e) {\n            return false;\n        }\n    }\n    // Clear authentication data\n    clearAuth() {\n        storage.remove(TOKEN_KEY);\n        storage.remove(ADMIN_KEY);\n        this.updateState({\n            isAuthenticated: false,\n            admin: null,\n            token: null,\n            isLoading: false,\n            error: null\n        });\n    }\n    // Get current auth state\n    getState() {\n        return {\n            ...this.authState\n        };\n    }\n    // Check if user has specific permission\n    hasPermission(resource, action) {\n        var _this_authState_admin;\n        if (!((_this_authState_admin = this.authState.admin) === null || _this_authState_admin === void 0 ? void 0 : _this_authState_admin.permissions)) return false;\n        const resourcePerms = this.authState.admin.permissions[resource];\n        if (!resourcePerms) return false;\n        return resourcePerms[action] === true;\n    }\n    // Check if user has role\n    hasRole(role) {\n        var _this_authState_admin;\n        return ((_this_authState_admin = this.authState.admin) === null || _this_authState_admin === void 0 ? void 0 : _this_authState_admin.role) === role;\n    }\n    // Get auth token\n    getToken() {\n        return this.authState.token;\n    }\n    // Get admin user\n    getAdmin() {\n        return this.authState.admin;\n    }\n    constructor(){\n        this.authState = {\n            isAuthenticated: false,\n            admin: null,\n            token: null,\n            isLoading: false,\n            error: null\n        };\n        this.listeners = [];\n        this.isInitialized = false;\n    }\n}\n// Export singleton instance\nconst adminAuth = AdminAuthService.getInstance();\n// React hook for auth state\nconst useAdminAuth = ()=>{\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0___default().useState(adminAuth.getState());\n    react__WEBPACK_IMPORTED_MODULE_0___default().useEffect({\n        \"useAdminAuth.useEffect\": ()=>{\n            const unsubscribe = adminAuth.subscribe(setState);\n            adminAuth.initialize(); // Initialize on first use\n            return unsubscribe;\n        }\n    }[\"useAdminAuth.useEffect\"], []);\n    return {\n        ...state,\n        login: adminAuth.login.bind(adminAuth),\n        logout: adminAuth.logout.bind(adminAuth),\n        hasPermission: adminAuth.hasPermission.bind(adminAuth),\n        hasRole: adminAuth.hasRole.bind(adminAuth)\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/adminAuth.ts\n"));

/***/ })

});