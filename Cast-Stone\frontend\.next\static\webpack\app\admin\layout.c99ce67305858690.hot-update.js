"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/app/admin/layout.tsx":
/*!**********************************!*\
  !*** ./src/app/admin/layout.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_AdminSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/admin/AdminSidebar */ \"(app-pages-browser)/./src/components/admin/AdminSidebar.tsx\");\n/* harmony import */ var _components_admin_AdminHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/admin/AdminHeader */ \"(app-pages-browser)/./src/components/admin/AdminHeader.tsx\");\n/* harmony import */ var _services_adminAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/adminAuth */ \"(app-pages-browser)/./src/services/adminAuth.ts\");\n/* harmony import */ var _layout_module_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./layout.module.css */ \"(app-pages-browser)/./src/app/admin/layout.module.css\");\n/* harmony import */ var _layout_module_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_layout_module_css__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdminLayout(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isAuthenticated, isLoading, admin } = (0,_services_adminAuth__WEBPACK_IMPORTED_MODULE_5__.useAdminAuth)();\n    // Debug logging\n    console.log('AdminLayout render:', {\n        pathname,\n        isAuthenticated,\n        isLoading,\n        hasAdmin: !!admin\n    });\n    // Public routes that don't require authentication\n    const publicRoutes = [\n        '/admin/login',\n        '/admin/change-password'\n    ];\n    const isPublicRoute = publicRoutes.includes(pathname);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminLayout.useEffect\": ()=>{\n            // Don't do anything while loading\n            if (isLoading) return;\n            // Redirect to login if not authenticated and not on public route\n            if (!isAuthenticated && !isPublicRoute) {\n                console.log('Redirecting to login - not authenticated');\n                router.push('/admin/login');\n                return;\n            }\n            // Redirect to dashboard if authenticated and on login page\n            if (isAuthenticated && pathname === '/admin/login') {\n                console.log('Redirecting to dashboard - already authenticated');\n                router.push('/admin/dashboard');\n                return;\n            }\n        }\n    }[\"AdminLayout.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        isPublicRoute,\n        pathname,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_6___default().loadingContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_6___default().spinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading admin...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this);\n    }\n    // Render public routes without admin layout\n    if (isPublicRoute) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // Render admin dashboard layout for authenticated routes\n    if (isAuthenticated && admin) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_6___default().adminLayout),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_6___default().mainContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_6___default().content),\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this);\n    }\n    // Show loading or redirect (handled by useEffect)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_6___default().loadingContainer),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_layout_module_css__WEBPACK_IMPORTED_MODULE_6___default().spinner)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Authenticating...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminLayout, \"bmzJYvEX8x4YvMoRZcxBawUIFkE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _services_adminAuth__WEBPACK_IMPORTED_MODULE_5__.useAdminAuth\n    ];\n});\n_c = AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/layout.tsx\n"));

/***/ })

});